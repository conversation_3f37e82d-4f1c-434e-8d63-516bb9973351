"""
用户管理页面
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import random
import plotly.express as px

from config.settings import AppConfig
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="用户管理 - Smart APS",
    page_icon="👥",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("admin.users"):
    st.error("权限不足，需要管理员权限")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 初始化会话状态
if 'selected_user_id' not in st.session_state:
    st.session_state.selected_user_id = None

if 'show_create_user_modal' not in st.session_state:
    st.session_state.show_create_user_modal = False

if 'show_edit_user_modal' not in st.session_state:
    st.session_state.show_edit_user_modal = False

if 'show_user_detail_modal' not in st.session_state:
    st.session_state.show_user_detail_modal = False

# 用户角色定义
USER_ROLES = {
    "admin": {
        "name": "管理员",
        "description": "系统全部功能权限",
        "color": "#FF6B6B",
        "icon": "👑",
        "permissions": [
            "admin.users", "admin.system", "admin.config",
            "planning.create", "planning.edit", "planning.view",
            "production.manage", "production.view",
            "equipment.manage", "equipment.view",
            "monitoring.manage", "monitoring.view",
            "analytics.advanced", "analytics.view",
            "llm.chat", "data.upload", "data.export"
        ]
    },
    "planner": {
        "name": "计划员",
        "description": "制定和调整生产计划",
        "color": "#4ECDC4",
        "icon": "📋",
        "permissions": [
            "planning.create", "planning.edit", "planning.view",
            "production.view", "equipment.view",
            "monitoring.view", "analytics.view",
            "llm.chat", "data.upload"
        ]
    },
    "operator": {
        "name": "主要用户",
        "description": "调整生产相关参数",
        "color": "#45B7D1",
        "icon": "⚙️",
        "permissions": [
            "production.manage", "production.view",
            "equipment.view", "monitoring.view",
            "analytics.view", "llm.chat"
        ]
    },
    "pci_user": {
        "name": "PCI用户",
        "description": "PCI页面数据上传和维护权限",
        "color": "#A29BFE",
        "icon": "🔬",
        "permissions": [
            "pci.view", "pci.upload", "pci.maintain",
            "pci.sql_query", "pci.data_export",
            "production.view", "monitoring.view",
            "analytics.view", "llm.chat"
        ]
    },
    "viewer": {
        "name": "一般用户",
        "description": "查看和对话权限",
        "color": "#FFEAA7",
        "icon": "👁️",
        "permissions": [
            "production.view", "equipment.view",
            "monitoring.view", "analytics.view",
            "llm.chat"
        ]
    }
}

# 页面标题
st.title("👥 用户管理")
st.markdown("### 管理系统用户、角色权限和访问控制")

# 侧边栏 - 用户管理配置
with st.sidebar:
    st.markdown("### ⚙️ 管理配置")

    # 筛选条件
    role_filter = st.selectbox(
        "角色筛选",
        ["全部"] + [role_info["name"] for role_info in USER_ROLES.values()],
        index=0
    )

    status_filter = st.selectbox(
        "状态筛选",
        ["全部", "活跃", "禁用", "待激活"],
        index=0
    )

    # 搜索
    search_query = st.text_input("🔍 搜索用户", placeholder="用户名、邮箱或姓名")

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("👤 创建用户", use_container_width=True):
        st.session_state.show_create_user_modal = True
        st.rerun()

    if st.button("🔄 刷新数据", use_container_width=True):
        st.rerun()

    if st.button("📊 用户统计", use_container_width=True):
        st.success("用户统计功能开发中...")

    if st.button("📋 导出用户", use_container_width=True):
        st.success("用户数据导出功能开发中...")

    st.markdown("---")

    # 角色说明
    st.markdown("### 📝 角色说明")

    for role_key, role_info in USER_ROLES.items():
        st.markdown(f"**{role_info['icon']} {role_info['name']}**")
        st.markdown(f"<small>{role_info['description']}</small>", unsafe_allow_html=True)
        st.markdown("")

# 辅助函数定义
def get_users_statistics():
    """获取用户统计"""
    try:
        result = api_client.get_users_statistics()

        if not result.get("success"):
            return get_mock_users_statistics()

        return result
    except Exception as e:
        return get_mock_users_statistics()


def get_users_list(role_filter=None, status_filter=None, search_query=None):
    """获取用户列表"""
    try:
        result = api_client.get_users_list(
            role_filter=role_filter,
            status_filter=status_filter,
            search_query=search_query
        )

        if not result.get("success"):
            return get_mock_users_list()

        return result
    except Exception as e:
        return get_mock_users_list()


def get_role_key_by_name(role_name):
    """根据角色名称获取角色键"""
    for role_key, role_info in USER_ROLES.items():
        if role_info["name"] == role_name:
            return role_key
    return None


def get_status_color(status):
    """获取状态颜色"""
    colors = {
        "活跃": "#32CD32",
        "禁用": "#FF6347",
        "待激活": "#FFA500",
        "unknown": "#808080"
    }
    return colors.get(status, "#808080")


def update_user_status(user_id, status):
    """更新用户状态"""
    try:
        result = api_client.update_user_status(user_id, status)
        if result.get("success"):
            st.success(f"用户状态已更新为: {status}")
            st.rerun()
        else:
            st.error(f"更新用户状态失败: {result.get('message', '未知错误')}")
    except Exception as e:
        st.error(f"更新用户状态失败: {str(e)}")


def reset_user_password(user_id):
    """重置用户密码"""
    try:
        result = api_client.reset_user_password(user_id)
        if result.get("success"):
            new_password = result.get("data", {}).get("new_password", "已重置")
            st.success(f"密码重置成功，新密码: {new_password}")
        else:
            st.error(f"重置密码失败: {result.get('message', '未知错误')}")
    except Exception as e:
        st.error(f"重置密码失败: {str(e)}")





def show_user_analytics():
    """显示用户分析"""
    # 获取用户统计数据
    stats_data = get_users_statistics()

    if stats_data and stats_data.get("success"):
        stats = stats_data.get("data", {})

        # 用户角色分布
        col1, col2 = st.columns(2)

        with col1:
            role_distribution = stats.get("role_distribution", {})
            if role_distribution:
                # 转换角色分布数据
                role_data = []
                for role_key, count in role_distribution.items():
                    role_info = USER_ROLES.get(role_key, {"name": role_key, "color": "#808080"})
                    role_data.append({
                        "角色": role_info["name"],
                        "数量": count,
                        "颜色": role_info["color"]
                    })

                df_roles = pd.DataFrame(role_data)

                fig_roles = px.pie(
                    df_roles,
                    values="数量",
                    names="角色",
                    title="📊 用户角色分布",
                    color_discrete_sequence=[item["颜色"] for item in role_data]
                )
                st.plotly_chart(fig_roles, use_container_width=True)
            else:
                st.info("暂无角色分布数据")

        with col2:
            # 用户活跃度趋势
            activity_trends = stats.get("activity_trends", [])
            if activity_trends:
                df_activity = pd.DataFrame(activity_trends)

                fig_activity = px.line(
                    df_activity,
                    x="date",
                    y="active_users",
                    title="📈 用户活跃度趋势",
                    markers=True
                )
                fig_activity.update_layout(
                    xaxis_title="日期",
                    yaxis_title="活跃用户数"
                )
                st.plotly_chart(fig_activity, use_container_width=True)
            else:
                st.info("暂无活跃度趋势数据")

        # 用户登录统计
        st.markdown("##### 📊 用户登录统计")

        login_stats = stats.get("login_statistics", {})
        if login_stats:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                daily_logins = login_stats.get("daily_logins", 0)
                st.metric("今日登录", daily_logins)

            with col2:
                weekly_logins = login_stats.get("weekly_logins", 0)
                st.metric("本周登录", weekly_logins)

            with col3:
                avg_session_time = login_stats.get("avg_session_time", 0)
                st.metric("平均会话时长", f"{avg_session_time:.1f}分钟")

            with col4:
                peak_concurrent = login_stats.get("peak_concurrent", 0)
                st.metric("峰值并发", peak_concurrent)

        # 最近活动用户
        st.markdown("##### 👥 最近活动用户")

        recent_users = stats.get("recent_active_users", [])
        if recent_users:
            for user in recent_users[:10]:  # 显示最近10个活跃用户
                col1, col2, col3 = st.columns([2, 1, 1])

                with col1:
                    role_info = USER_ROLES.get(user.get("role", "viewer"), USER_ROLES["viewer"])
                    st.markdown(f"**{role_info['icon']} {user.get('username', 'Unknown')}**")

                with col2:
                    st.markdown(f"<span style='color: {role_info['color']}'>{role_info['name']}</span>",
                              unsafe_allow_html=True)

                with col3:
                    last_activity = user.get("last_activity", "")
                    if last_activity:
                        try:
                            activity_time = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                            last_activity = activity_time.strftime("%m-%d %H:%M")
                        except:
                            pass
                    st.text(last_activity)
        else:
            st.info("暂无最近活动用户数据")

    else:
        st.error("获取用户分析数据失败")


# 模拟数据函数
def get_mock_users_statistics():
    """获取模拟用户统计数据"""
    # 生成活跃度趋势数据
    activity_trends = []
    base_date = datetime.now() - timedelta(days=30)

    for i in range(30):
        date = base_date + timedelta(days=i)
        active_users = 15 + random.randint(-5, 10)
        activity_trends.append({
            "date": date.strftime("%Y-%m-%d"),
            "active_users": active_users
        })

    return {
        "success": True,
        "data": {
            "total_users": 25,
            "active_users": 18,
            "new_users_today": 2,
            "online_users": 8,
            "disabled_users": 3,
            "role_distribution": {
                "admin": 2,
                "planner": 4,
                "operator": 8,
                "pci_user": 3,
                "viewer": 8
            },
            "activity_trends": activity_trends,
            "login_statistics": {
                "daily_logins": 45,
                "weekly_logins": 280,
                "avg_session_time": 125.5,
                "peak_concurrent": 12
            },
            "recent_active_users": [
                {"username": "张三", "role": "admin", "last_activity": "2024-01-15T14:30:00Z"},
                {"username": "李四", "role": "planner", "last_activity": "2024-01-15T14:25:00Z"},
                {"username": "王五", "role": "operator", "last_activity": "2024-01-15T14:20:00Z"},
                {"username": "赵六", "role": "pci_user", "last_activity": "2024-01-15T14:15:00Z"},
                {"username": "钱七", "role": "viewer", "last_activity": "2024-01-15T14:10:00Z"}
            ]
        }
    }


def get_mock_users_list():
    """获取模拟用户列表数据"""
    return {
        "success": True,
        "data": {
            "users": [
                {
                    "id": "user_001",
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "系统管理员",
                    "role": "admin",
                    "status": "活跃",
                    "last_login": "2024-01-15T14:30:00Z",
                    "created_at": "2024-01-01T00:00:00Z"
                },
                {
                    "id": "user_002",
                    "username": "planner01",
                    "email": "<EMAIL>",
                    "full_name": "张计划",
                    "role": "planner",
                    "status": "活跃",
                    "last_login": "2024-01-15T13:45:00Z",
                    "created_at": "2024-01-02T00:00:00Z"
                },
                {
                    "id": "user_003",
                    "username": "operator01",
                    "email": "<EMAIL>",
                    "full_name": "李操作",
                    "role": "operator",
                    "status": "活跃",
                    "last_login": "2024-01-15T12:20:00Z",
                    "created_at": "2024-01-03T00:00:00Z"
                },
                {
                    "id": "user_004",
                    "username": "pci_user01",
                    "email": "<EMAIL>",
                    "full_name": "王PCI",
                    "role": "pci_user",
                    "status": "活跃",
                    "last_login": "2024-01-15T11:15:00Z",
                    "created_at": "2024-01-04T00:00:00Z"
                },
                {
                    "id": "user_005",
                    "username": "viewer01",
                    "email": "<EMAIL>",
                    "full_name": "赵查看",
                    "role": "viewer",
                    "status": "禁用",
                    "last_login": "2024-01-14T16:30:00Z",
                    "created_at": "2024-01-05T00:00:00Z"
                }
            ],
            "total": 5
        }
    }


# 主要内容区域
tab1, tab2, tab3 = st.tabs(["👥 用户列表", "🔐 权限管理", "📊 用户分析"])

with tab1:
    st.markdown("#### 👥 用户列表管理")

    # 获取用户统计
    user_stats = get_users_statistics()

    if user_stats and user_stats.get("success"):
        stats = user_stats.get("data", {})

        # 用户统计概览
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            total_users = stats.get("total_users", 0)
            st.metric("总用户数", total_users)

        with col2:
            active_users = stats.get("active_users", 0)
            st.metric("活跃用户", active_users, delta=f"{active_users/total_users*100:.1f}%" if total_users > 0 else "0%")

        with col3:
            new_users_today = stats.get("new_users_today", 0)
            st.metric("今日新增", new_users_today)

        with col4:
            online_users = stats.get("online_users", 0)
            st.metric("在线用户", online_users)

        with col5:
            disabled_users = stats.get("disabled_users", 0)
            st.metric("禁用用户", disabled_users, delta_color="inverse" if disabled_users > 0 else "normal")

    st.markdown("---")

    # 获取用户列表
    users_data = get_users_list(
        role_filter=get_role_key_by_name(role_filter) if role_filter != "全部" else None,
        status_filter=status_filter if status_filter != "全部" else None,
        search_query=search_query if search_query else None
    )

    if users_data and users_data.get("success"):
        users = users_data.get("data", {}).get("users", [])
        total_count = users_data.get("data", {}).get("total", 0)

        if users:
            st.markdown(f"##### 📋 用户列表 (共 {total_count} 个用户)")

            # 用户列表表格
            for user in users:
                with st.container():
                    col1, col2, col3, col4, col5, col6 = st.columns([2, 1.5, 1, 1, 1, 2])

                    with col1:
                        role_info = USER_ROLES.get(user.get("role", "viewer"), USER_ROLES["viewer"])
                        st.markdown(f"**{role_info['icon']} {user.get('username', 'Unknown')}**")
                        st.markdown(f"<small>{user.get('email', 'No email')}</small>", unsafe_allow_html=True)
                        st.markdown(f"<small>{user.get('full_name', 'No name')}</small>", unsafe_allow_html=True)

                    with col2:
                        role_color = role_info["color"]
                        st.markdown(f"<span style='color: {role_color}; font-weight: bold;'>{role_info['name']}</span>",
                                  unsafe_allow_html=True)

                    with col3:
                        status = user.get("status", "unknown")
                        status_color = get_status_color(status)
                        st.markdown(f"<span style='color: {status_color}'>● {status}</span>",
                                  unsafe_allow_html=True)

                    with col4:
                        last_login = user.get("last_login", "从未登录")
                        if last_login != "从未登录":
                            try:
                                login_time = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                                last_login = login_time.strftime("%m-%d %H:%M")
                            except:
                                pass
                        st.text(last_login)

                    with col5:
                        created_at = user.get("created_at", "")
                        if created_at:
                            try:
                                create_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                created_at = create_time.strftime("%Y-%m-%d")
                            except:
                                pass
                        st.text(created_at)

                    with col6:
                        col_a, col_b, col_c, col_d = st.columns(4)

                        with col_a:
                            if st.button("👁️", key=f"view_{user.get('id')}", help="查看详情"):
                                st.session_state.selected_user_id = user.get('id')
                                st.session_state.show_user_detail_modal = True
                                st.rerun()

                        with col_b:
                            if st.button("✏️", key=f"edit_{user.get('id')}", help="编辑用户"):
                                st.session_state.selected_user_id = user.get('id')
                                st.session_state.show_edit_user_modal = True
                                st.rerun()

                        with col_c:
                            if user.get("status") == "活跃":
                                if st.button("🚫", key=f"disable_{user.get('id')}", help="禁用用户"):
                                    update_user_status(user.get('id'), "禁用")
                            else:
                                if st.button("✅", key=f"enable_{user.get('id')}", help="启用用户"):
                                    update_user_status(user.get('id'), "活跃")

                        with col_d:
                            if st.button("🔑", key=f"reset_{user.get('id')}", help="重置密码"):
                                reset_user_password(user.get('id'))

                    st.markdown("---")
        else:
            st.info("暂无用户数据")

    else:
        st.error("获取用户列表失败")

with tab2:
    st.markdown("#### 🔐 权限管理")

    # 角色权限矩阵
    st.markdown("##### 📊 角色权限矩阵")

    # 创建权限矩阵表格
    all_permissions = set()
    for role_info in USER_ROLES.values():
        all_permissions.update(role_info["permissions"])

    permission_matrix = []
    for permission in sorted(all_permissions):
        row = {"权限": permission}
        for role_key, role_info in USER_ROLES.items():
            row[role_info["name"]] = "✅" if permission in role_info["permissions"] else "❌"
        permission_matrix.append(row)

    if permission_matrix:
        df_permissions = pd.DataFrame(permission_matrix)
        st.dataframe(df_permissions, use_container_width=True, hide_index=True)

    st.markdown("---")

    # 权限说明
    st.markdown("##### 📝 权限说明")

    permission_descriptions = {
        "admin.users": "用户管理 - 创建、编辑、删除用户",
        "admin.system": "系统管理 - 系统配置和维护",
        "admin.config": "配置管理 - 系统参数配置",
        "planning.create": "计划创建 - 创建生产计划",
        "planning.edit": "计划编辑 - 修改生产计划",
        "planning.view": "计划查看 - 查看生产计划",
        "production.manage": "生产管理 - 调整生产参数",
        "production.view": "生产查看 - 查看生产信息",
        "equipment.manage": "设备管理 - 设备操作和维护",
        "equipment.view": "设备查看 - 查看设备状态",
        "monitoring.manage": "监控管理 - 处理告警和调整",
        "monitoring.view": "监控查看 - 查看监控数据",
        "analytics.advanced": "高级分析 - 预测分析等高级功能",
        "analytics.view": "分析查看 - 查看分析报告",
        "llm.chat": "AI对话 - 使用智能助手",
        "data.upload": "数据上传 - 上传文件和数据",
        "data.export": "数据导出 - 导出系统数据"
    }

    for permission, description in permission_descriptions.items():
        st.markdown(f"**{permission}**: {description}")

with tab3:
    st.markdown("#### 📊 用户分析")

    show_user_analytics()


# 用户操作函数
def create_user(user_data):
    """创建用户"""
    try:
        result = api_client.create_user(user_data)
        if result.get("success"):
            return True
        else:
            st.error(f"创建用户失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"创建用户失败: {str(e)}")
        return False


def update_user(user_id, user_data):
    """更新用户"""
    try:
        result = api_client.update_user(user_id, user_data)
        if result.get("success"):
            return True
        else:
            st.error(f"更新用户失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"更新用户失败: {str(e)}")
        return False


def get_user_detail(user_id):
    """获取用户详情"""
    try:
        result = api_client.get_user_detail(user_id)

        if not result.get("success"):
            # 返回模拟数据
            mock_users = get_mock_users_list()
            users = mock_users.get("data", {}).get("users", [])
            for user in users:
                if user.get("id") == user_id:
                    return {"success": True, "data": user}
            return {"success": False, "message": "用户不存在"}

        return result
    except Exception as e:
        return {"success": False, "message": str(e)}


def get_user_activity_log(user_id):
    """获取用户活动日志"""
    try:
        result = api_client.get_user_activity_log(user_id)

        if not result.get("success"):
            # 返回模拟活动日志
            return {
                "success": True,
                "data": {
                    "activities": [
                        {"timestamp": "2024-01-15T14:30:00Z", "action": "登录系统"},
                        {"timestamp": "2024-01-15T14:25:00Z", "action": "查看生产计划"},
                        {"timestamp": "2024-01-15T14:20:00Z", "action": "使用智能助手"},
                        {"timestamp": "2024-01-15T14:15:00Z", "action": "导出数据"},
                        {"timestamp": "2024-01-15T14:10:00Z", "action": "修改个人信息"}
                    ]
                }
            }

        return result
    except Exception as e:
        return {"success": False, "message": str(e)}


# 模态框函数
@st.dialog("👤 创建用户")
def show_create_user_modal():
    """显示创建用户模态框"""
    st.markdown("### 创建新用户")

    with st.form("create_user_form"):
        col1, col2 = st.columns(2)

        with col1:
            username = st.text_input("用户名 *", placeholder="请输入用户名")
            email = st.text_input("邮箱 *", placeholder="请输入邮箱地址")
            full_name = st.text_input("姓名 *", placeholder="请输入真实姓名")

        with col2:
            role = st.selectbox("角色 *",
                               options=list(USER_ROLES.keys()),
                               format_func=lambda x: f"{USER_ROLES[x]['icon']} {USER_ROLES[x]['name']}")

            password = st.text_input("密码 *", type="password", placeholder="请输入密码")
            confirm_password = st.text_input("确认密码 *", type="password", placeholder="请再次输入密码")

        # 权限预览
        st.markdown("##### 🔐 角色权限预览")
        selected_role_info = USER_ROLES[role]
        st.markdown(f"**{selected_role_info['icon']} {selected_role_info['name']}**: {selected_role_info['description']}")

        permissions_text = ", ".join(selected_role_info['permissions'][:5])
        if len(selected_role_info['permissions']) > 5:
            permissions_text += f" 等 {len(selected_role_info['permissions'])} 项权限"
        st.markdown(f"**权限**: {permissions_text}")

        col1, col2 = st.columns(2)

        with col1:
            if st.form_submit_button("✅ 创建用户", use_container_width=True):
                # 验证输入
                if not all([username, email, full_name, password, confirm_password]):
                    st.error("请填写所有必填字段")
                elif password != confirm_password:
                    st.error("两次输入的密码不一致")
                elif len(password) < 6:
                    st.error("密码长度至少6位")
                else:
                    # 创建用户
                    user_data = {
                        "username": username,
                        "email": email,
                        "full_name": full_name,
                        "role": role,
                        "password": password
                    }

                    result = create_user(user_data)
                    if result:
                        st.success("用户创建成功！")
                        st.session_state.show_create_user_modal = False
                        st.rerun()

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_create_user_modal = False
                st.rerun()


@st.dialog("✏️ 编辑用户")
def show_edit_user_modal():
    """显示编辑用户模态框"""
    user_id = st.session_state.selected_user_id

    if not user_id:
        st.error("未选择用户")
        return

    # 获取用户详情
    user_detail = get_user_detail(user_id)

    if not user_detail or not user_detail.get("success"):
        st.error("获取用户信息失败")
        return

    user = user_detail.get("data", {})

    st.markdown(f"### 编辑用户: {user.get('username', 'Unknown')}")

    with st.form("edit_user_form"):
        col1, col2 = st.columns(2)

        with col1:
            username = st.text_input("用户名", value=user.get('username', ''), disabled=True)
            email = st.text_input("邮箱 *", value=user.get('email', ''))
            full_name = st.text_input("姓名 *", value=user.get('full_name', ''))

        with col2:
            current_role = user.get('role', 'viewer')
            role = st.selectbox("角色 *",
                               options=list(USER_ROLES.keys()),
                               index=list(USER_ROLES.keys()).index(current_role),
                               format_func=lambda x: f"{USER_ROLES[x]['icon']} {USER_ROLES[x]['name']}")

            current_status = user.get('status', '活跃')
            status = st.selectbox("状态", ["活跃", "禁用", "待激活"],
                                index=["活跃", "禁用", "待激活"].index(current_status) if current_status in ["活跃", "禁用", "待激活"] else 0)

        # 权限变更提示
        if role != current_role:
            st.markdown("##### ⚠️ 权限变更提示")
            old_role_info = USER_ROLES[current_role]
            new_role_info = USER_ROLES[role]
            st.markdown(f"**当前角色**: {old_role_info['icon']} {old_role_info['name']}")
            st.markdown(f"**新角色**: {new_role_info['icon']} {new_role_info['name']}")

        col1, col2 = st.columns(2)

        with col1:
            if st.form_submit_button("✅ 保存修改", use_container_width=True):
                # 验证输入
                if not all([email, full_name]):
                    st.error("请填写所有必填字段")
                else:
                    # 更新用户
                    user_data = {
                        "email": email,
                        "full_name": full_name,
                        "role": role,
                        "status": status
                    }

                    result = update_user(user_id, user_data)
                    if result:
                        st.success("用户信息更新成功！")
                        st.session_state.show_edit_user_modal = False
                        st.rerun()

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_edit_user_modal = False
                st.rerun()


@st.dialog("👁️ 用户详情")
def show_user_detail_modal():
    """显示用户详情模态框"""
    user_id = st.session_state.selected_user_id

    if not user_id:
        st.error("未选择用户")
        return

    # 获取用户详情
    user_detail = get_user_detail(user_id)

    if not user_detail or not user_detail.get("success"):
        st.error("获取用户信息失败")
        return

    user = user_detail.get("data", {})
    role_info = USER_ROLES.get(user.get("role", "viewer"), USER_ROLES["viewer"])

    st.markdown(f"### {role_info['icon']} {user.get('username', 'Unknown')}")

    # 基本信息
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📋 基本信息")
        st.markdown(f"**用户名**: {user.get('username', 'N/A')}")
        st.markdown(f"**邮箱**: {user.get('email', 'N/A')}")
        st.markdown(f"**姓名**: {user.get('full_name', 'N/A')}")
        st.markdown(f"**角色**: {role_info['icon']} {role_info['name']}")

        status = user.get('status', 'unknown')
        status_color = get_status_color(status)
        st.markdown(f"**状态**: <span style='color: {status_color}'>● {status}</span>",
                   unsafe_allow_html=True)

    with col2:
        st.markdown("##### 📊 活动信息")

        created_at = user.get('created_at', '')
        if created_at:
            try:
                create_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_at = create_time.strftime("%Y-%m-%d %H:%M:%S")
            except:
                pass
        st.markdown(f"**创建时间**: {created_at}")

        last_login = user.get('last_login', '从未登录')
        if last_login != '从未登录':
            try:
                login_time = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                last_login = login_time.strftime("%Y-%m-%d %H:%M:%S")
            except:
                pass
        st.markdown(f"**最后登录**: {last_login}")

        login_count = user.get('login_count', 0)
        st.markdown(f"**登录次数**: {login_count}")

    # 权限信息
    st.markdown("##### 🔐 权限信息")
    st.markdown(f"**角色描述**: {role_info['description']}")

    permissions = role_info['permissions']
    st.markdown(f"**权限列表** ({len(permissions)} 项):")

    # 分列显示权限
    cols = st.columns(3)
    for i, permission in enumerate(permissions):
        with cols[i % 3]:
            st.markdown(f"• {permission}")

    # 活动日志
    st.markdown("##### 📝 最近活动")

    activity_log = get_user_activity_log(user_id)
    if activity_log and activity_log.get("success"):
        activities = activity_log.get("data", {}).get("activities", [])

        if activities:
            for activity in activities[:5]:  # 显示最近5条活动
                activity_time = activity.get('timestamp', '')
                if activity_time:
                    try:
                        time_obj = datetime.fromisoformat(activity_time.replace('Z', '+00:00'))
                        activity_time = time_obj.strftime("%m-%d %H:%M")
                    except:
                        pass

                st.markdown(f"**{activity_time}** - {activity.get('action', 'Unknown action')}")
        else:
            st.info("暂无活动记录")
    else:
        st.info("获取活动日志失败")

    # 关闭按钮
    if st.button("❌ 关闭", use_container_width=True):
        st.session_state.show_user_detail_modal = False
        st.rerun()


# 模态框处理 - 在所有函数定义之后调用
if st.session_state.get('show_create_user_modal'):
    show_create_user_modal()

if st.session_state.get('show_edit_user_modal'):
    show_edit_user_modal()

if st.session_state.get('show_user_detail_modal'):
    show_user_detail_modal()


