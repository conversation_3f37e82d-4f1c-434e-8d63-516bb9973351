"""
认证配置管理页面
支持LDAP和SSO配置管理
"""

import streamlit as st
import json
from datetime import datetime

from utils.auth import check_authentication, require_permission
from utils.i18n import i18n

# 页面配置
st.set_page_config(
    page_title="认证配置 - Smart APS",
    page_icon="🔐",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录" if i18n.get_current_language() == "zh" else "Please login first")
    st.stop()

# 检查权限
if not require_permission("admin"):
    st.error("需要管理员权限" if i18n.get_current_language() == "zh" else "Admin permission required")
    st.stop()

# 页面标题
if i18n.get_current_language() == "en":
    st.title("🔐 Authentication Configuration")
    st.markdown("### Configure LDAP and SSO authentication methods")
else:
    st.title("🔐 认证配置管理")
    st.markdown("### 配置LDAP和SSO认证方式")

# 侧边栏
with st.sidebar:
    # 语言选择器
    i18n.language_selector("auth_config_language_selector")

    st.markdown("---")

    # 认证状态
    st.markdown("### 🔍 认证状态")

    # 动态获取认证状态
    if 'auth_config' not in st.session_state:
        st.session_state.auth_config = {
            "local": {"enabled": True, "status": "正常"},
            "ldap": {"enabled": False, "status": "未启用"},
            "sso": {"enabled": False, "status": "未启用"}
        }

    auth_status = st.session_state.auth_config

    for auth_type, status in auth_status.items():
        status_icon = "🟢" if status["enabled"] else "🔴"
        st.write(f"{status_icon} **{auth_type.upper()}**: {status['status']}")

    st.markdown("---")

    # 测试按钮
    st.markdown("### 🧪 连接测试")

    if st.button("测试LDAP连接", use_container_width=True):
        st.session_state.test_ldap = True

    if st.button("测试SSO配置", use_container_width=True):
        st.session_state.test_sso = True

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs([
    i18n.t("auth_overview", default="认证概览"),
    i18n.t("ldap_config", default="LDAP配置"),
    i18n.t("sso_config", default="SSO配置"),
    i18n.t("auth_test", default="认证测试")
])

with tab1:
    st.markdown("#### 🔐 认证方式概览")

    # 认证方式对比
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("##### 🏠 本地认证")
        st.info("""
        **特点**:
        - 用户名/密码认证
        - 本地数据库存储
        - 完全自主控制

        **适用场景**:
        - 小型团队
        - 独立部署
        - 简单认证需求
        """)

        st.metric("状态", "✅ 已启用")
        st.metric("用户数", "15")

    with col2:
        st.markdown("##### 🌐 LDAP认证")
        st.info("""
        **特点**:
        - 企业目录集成
        - 统一用户管理
        - 支持AD/OpenLDAP

        **适用场景**:
        - 企业环境
        - 统一身份管理
        - 大量用户
        """)

        status = "❌ 未启用" if not auth_status["ldap"]["enabled"] else "✅ 已启用"
        st.metric("状态", status)
        st.metric("配置状态", auth_status["ldap"]["status"])

    with col3:
        st.markdown("##### 🔑 SSO单点登录")
        st.info("""
        **特点**:
        - 单点登录体验
        - 支持SAML/OAuth
        - 第三方身份提供商

        **适用场景**:
        - 多系统集成
        - 企业级安全
        - 现代化认证
        """)

        status = "❌ 未启用" if not auth_status["sso"]["enabled"] else "✅ 已启用"
        st.metric("状态", status)
        st.metric("配置状态", auth_status["sso"]["status"])

    # 认证流程图
    st.markdown("#### 🔄 认证流程")

    st.markdown("""
    ```mermaid
    graph TD
        A[用户登录] --> B{认证方式选择}
        B -->|本地认证| C[用户名密码验证]
        B -->|LDAP认证| D[LDAP服务器验证]
        B -->|SSO认证| E[身份提供商验证]

        C --> F[本地数据库验证]
        D --> G[LDAP目录验证]
        E --> H[SAML/OAuth验证]

        F --> I{验证结果}
        G --> I
        H --> I

        I -->|成功| J[生成JWT令牌]
        I -->|失败| K[返回错误]

        J --> L[用户登录成功]
        K --> M[登录失败]
    ```
    """)

with tab2:
    st.markdown("#### 🌐 LDAP配置")

    # LDAP基础配置
    with st.expander("🔧 基础配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            ldap_enabled = st.checkbox("启用LDAP认证", value=False)
            ldap_server = st.text_input("LDAP服务器", value="ldap://localhost:389")
            ldap_port = st.number_input("端口", min_value=1, max_value=65535, value=389)
            ldap_use_ssl = st.checkbox("使用SSL", value=False)
            ldap_use_tls = st.checkbox("使用TLS", value=False)

        with col2:
            ldap_bind_dn = st.text_input("绑定DN", value="cn=admin,dc=example,dc=com")
            ldap_bind_password = st.text_input("绑定密码", type="password")
            ldap_auth_method = st.selectbox("认证方法", ["SIMPLE", "NTLM"], index=0)
            ldap_base_dn = st.text_input("基础DN", value="dc=example,dc=com")

    # LDAP搜索配置
    with st.expander("🔍 搜索配置"):
        col1, col2 = st.columns(2)

        with col1:
            ldap_user_search_base = st.text_input("用户搜索基础", value="ou=users,dc=example,dc=com")
            ldap_user_filter = st.text_input("用户过滤器", value="(uid={username})")
            ldap_username_attr = st.text_input("用户名属性", value="uid")
            ldap_email_attr = st.text_input("邮箱属性", value="mail")

        with col2:
            ldap_group_search_base = st.text_input("组搜索基础", value="ou=groups,dc=example,dc=com")
            ldap_group_filter = st.text_input("组过滤器", value="(member={user_dn})")
            ldap_first_name_attr = st.text_input("名字属性", value="givenName")
            ldap_last_name_attr = st.text_input("姓氏属性", value="sn")

    # LDAP权限映射
    with st.expander("👥 权限映射"):
        col1, col2 = st.columns(2)

        with col1:
            ldap_admin_groups = st.text_area("管理员组", value="administrators\nadmin")
            ldap_auto_create = st.checkbox("自动创建用户", value=True)

        with col2:
            ldap_user_groups = st.text_area("普通用户组", value="users\nemployees")
            ldap_auto_update = st.checkbox("自动更新用户", value=True)

    # 保存LDAP配置
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 保存LDAP配置", type="primary", use_container_width=True):
            ldap_config = {
                "enabled": ldap_enabled,
                "server": ldap_server,
                "port": ldap_port,
                "use_ssl": ldap_use_ssl,
                "use_tls": ldap_use_tls,
                "bind_dn": ldap_bind_dn,
                "bind_password": ldap_bind_password,
                "auth_method": ldap_auth_method,
                "base_dn": ldap_base_dn,
                "user_search_base": ldap_user_search_base,
                "user_filter": ldap_user_filter,
                "username_attr": ldap_username_attr,
                "email_attr": ldap_email_attr,
                "group_search_base": ldap_group_search_base,
                "group_filter": ldap_group_filter,
                "admin_groups": ldap_admin_groups.split('\n'),
                "user_groups": ldap_user_groups.split('\n'),
                "auto_create": ldap_auto_create,
                "auto_update": ldap_auto_update
            }

            # 更新会话状态
            st.session_state.auth_config["ldap"]["enabled"] = ldap_enabled
            st.session_state.auth_config["ldap"]["status"] = "已配置" if ldap_enabled else "未启用"

            st.success("✅ LDAP配置已保存！")
            st.json(ldap_config)

    with col2:
        # 快速启用/禁用按钮
        current_ldap_status = st.session_state.auth_config["ldap"]["enabled"]
        action_text = "🔴 禁用LDAP" if current_ldap_status else "🟢 启用LDAP"

        if st.button(action_text, use_container_width=True):
            new_status = not current_ldap_status
            st.session_state.auth_config["ldap"]["enabled"] = new_status
            st.session_state.auth_config["ldap"]["status"] = "已启用" if new_status else "已禁用"

            status_msg = "✅ LDAP已启用" if new_status else "❌ LDAP已禁用"
            st.success(status_msg)
            st.rerun()

with tab3:
    st.markdown("#### 🔑 SSO配置")

    # SSO类型选择
    sso_enabled = st.checkbox("启用SSO认证", value=False)
    sso_type = st.selectbox("SSO类型", ["SAML 2.0", "OAuth 2.0", "OpenID Connect"], index=0)

    if sso_type == "SAML 2.0":
        # SAML配置
        with st.expander("🔐 SAML配置", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                saml_idp_url = st.text_input("身份提供商URL", value="")
                saml_sp_entity_id = st.text_input("服务提供商实体ID", value="smart-aps")
                saml_acs_url = st.text_input("断言消费服务URL", value="http://localhost:8000/api/v1/auth/saml/acs")

            with col2:
                saml_sls_url = st.text_input("单点登出URL", value="http://localhost:8000/api/v1/auth/saml/sls")
                saml_idp_cert = st.text_area("身份提供商证书", height=100)

    elif sso_type in ["OAuth 2.0", "OpenID Connect"]:
        # OAuth/OIDC配置
        with st.expander("🔑 OAuth/OIDC配置", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                oauth_client_id = st.text_input("客户端ID", value="")
                oauth_client_secret = st.text_input("客户端密钥", type="password")
                oauth_auth_url = st.text_input("授权URL", value="")
                oauth_token_url = st.text_input("令牌URL", value="")

            with col2:
                oauth_userinfo_url = st.text_input("用户信息URL", value="")
                oauth_redirect_uri = st.text_input("重定向URI", value="http://localhost:8000/api/v1/auth/oauth/callback")
                oauth_scope = st.text_input("权限范围", value="openid profile email")

    # SSO属性映射
    with st.expander("📋 属性映射"):
        col1, col2 = st.columns(2)

        with col1:
            sso_username_claim = st.text_input("用户名声明", value="sub")
            sso_email_claim = st.text_input("邮箱声明", value="email")

        with col2:
            sso_name_claim = st.text_input("姓名声明", value="name")
            sso_groups_claim = st.text_input("组声明", value="groups")

    # 保存SSO配置
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 保存SSO配置", type="primary", use_container_width=True):
            if sso_type == "SAML 2.0":
                sso_config = {
                    "enabled": sso_enabled,
                    "type": "saml",
                    "idp_url": saml_idp_url,
                    "sp_entity_id": saml_sp_entity_id,
                    "acs_url": saml_acs_url,
                    "sls_url": saml_sls_url,
                    "idp_cert": saml_idp_cert,
                    "username_claim": sso_username_claim,
                    "email_claim": sso_email_claim,
                    "name_claim": sso_name_claim,
                    "groups_claim": sso_groups_claim
                }
            else:
                sso_config = {
                    "enabled": sso_enabled,
                    "type": "oauth" if sso_type == "OAuth 2.0" else "oidc",
                    "client_id": oauth_client_id,
                    "client_secret": oauth_client_secret,
                    "auth_url": oauth_auth_url,
                    "token_url": oauth_token_url,
                    "userinfo_url": oauth_userinfo_url,
                    "redirect_uri": oauth_redirect_uri,
                    "scope": oauth_scope,
                    "username_claim": sso_username_claim,
                    "email_claim": sso_email_claim,
                    "name_claim": sso_name_claim,
                    "groups_claim": sso_groups_claim
                }

            # 更新会话状态
            st.session_state.auth_config["sso"]["enabled"] = sso_enabled
            st.session_state.auth_config["sso"]["status"] = "已配置" if sso_enabled else "未启用"

            st.success("✅ SSO配置已保存！")
            st.json(sso_config)

    with col2:
        # 快速启用/禁用按钮
        current_sso_status = st.session_state.auth_config["sso"]["enabled"]
        action_text = "🔴 禁用SSO" if current_sso_status else "🟢 启用SSO"

        if st.button(action_text, use_container_width=True):
            new_status = not current_sso_status
            st.session_state.auth_config["sso"]["enabled"] = new_status
            st.session_state.auth_config["sso"]["status"] = "已启用" if new_status else "已禁用"

            status_msg = "✅ SSO已启用" if new_status else "❌ SSO已禁用"
            st.success(status_msg)
            st.rerun()

with tab4:
    st.markdown("#### 🧪 认证测试")

    # LDAP连接测试
    st.markdown("##### 🌐 LDAP连接测试")

    if st.session_state.get("test_ldap", False):
        with st.spinner("正在测试LDAP连接..."):
            # 模拟LDAP测试
            import time
            time.sleep(2)

            test_result = {
                "success": True,
                "message": "LDAP连接测试成功",
                "server": "ldap://localhost:389",
                "base_dn": "dc=example,dc=com",
                "response_time": "150ms"
            }

            if test_result["success"]:
                st.success(f"✅ {test_result['message']}")
                st.info(f"服务器: {test_result['server']}")
                st.info(f"基础DN: {test_result['base_dn']}")
                st.info(f"响应时间: {test_result['response_time']}")
            else:
                st.error(f"❌ {test_result['message']}")

        st.session_state.test_ldap = False

    # SSO配置测试
    st.markdown("##### 🔑 SSO配置测试")

    if st.session_state.get("test_sso", False):
        with st.spinner("正在测试SSO配置..."):
            # 模拟SSO测试
            import time
            time.sleep(2)

            test_result = {
                "success": False,
                "message": "SSO未配置",
                "type": "未设置"
            }

            if test_result["success"]:
                st.success(f"✅ {test_result['message']}")
                st.info(f"SSO类型: {test_result['type']}")
            else:
                st.warning(f"⚠️ {test_result['message']}")

        st.session_state.test_sso = False

    # 用户认证测试
    st.markdown("##### 👤 用户认证测试")

    with st.form("auth_test_form"):
        col1, col2 = st.columns(2)

        with col1:
            test_username = st.text_input("测试用户名")
            test_password = st.text_input("测试密码", type="password")

        with col2:
            test_auth_method = st.selectbox("认证方式", ["自动检测", "本地认证", "LDAP认证"])

        if st.form_submit_button("🧪 测试认证"):
            if test_username and test_password:
                with st.spinner("正在测试用户认证..."):
                    # 模拟认证测试
                    import time
                    time.sleep(1)

                    if test_username == "admin" and test_password == "admin":
                        st.success("✅ 认证成功")
                        st.info(f"用户: {test_username}")
                        st.info(f"认证方式: {test_auth_method}")
                        st.info("权限: 管理员")
                    else:
                        st.error("❌ 认证失败")
                        st.warning("用户名或密码错误")
            else:
                st.warning("请输入用户名和密码")

# 页面底部信息
st.markdown("---")
if i18n.get_current_language() == "en":
    st.markdown("💡 **Tip**: Configure LDAP and SSO to enable enterprise-level authentication integration.")
else:
    st.markdown("💡 **提示**: 配置LDAP和SSO以启用企业级认证集成。")
