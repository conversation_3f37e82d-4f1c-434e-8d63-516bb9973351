"""
图表工具函数
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from config.theme import apply_plotly_theme
from config.settings import CHART_CONFIG


def create_gantt_chart(
    gantt_data: Dict[str, Any],
    view_mode: str = "按设备",
    time_unit: str = "天",
    show_critical_path: bool = True,
    show_delays: bool = True,
    height: int = 600
) -> go.Figure:
    """创建甘特图"""
    
    tasks = gantt_data.get("tasks", [])
    if not tasks:
        # 返回空图表
        fig = go.Figure()
        fig.add_annotation(
            text="暂无数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=20, color="gray")
        )
        return apply_plotly_theme(fig)
    
    # 转换数据格式
    df_tasks = pd.DataFrame(tasks)
    df_tasks['start'] = pd.to_datetime(df_tasks['start'])
    df_tasks['end'] = pd.to_datetime(df_tasks['end'])
    df_tasks['duration'] = (df_tasks['end'] - df_tasks['start']).dt.days
    
    # 根据视图模式分组
    if view_mode == "按设备":
        group_col = "resource"
        color_col = "resource"
    elif view_mode == "按订单":
        group_col = "name"
        color_col = "name"
    else:  # 按产品
        group_col = "product" if "product" in df_tasks.columns else "name"
        color_col = group_col
    
    # 创建甘特图
    fig = px.timeline(
        df_tasks,
        x_start="start",
        x_end="end",
        y=group_col,
        color=color_col,
        title=f"生产计划甘特图 - {view_mode}",
        hover_data=["duration"]
    )
    
    # 自定义样式
    fig.update_yaxes(categoryorder="total ascending")
    fig.update_layout(
        height=height,
        xaxis_title="时间",
        yaxis_title=view_mode.replace("按", ""),
        showlegend=True
    )
    
    # 添加关键路径
    if show_critical_path and "critical_path" in gantt_data:
        critical_tasks = gantt_data["critical_path"]
        for task_id in critical_tasks:
            task = next((t for t in tasks if t["id"] == task_id), None)
            if task:
                fig.add_shape(
                    type="rect",
                    x0=task["start"],
                    x1=task["end"],
                    y0=task[group_col],
                    y1=task[group_col],
                    line=dict(color="red", width=3),
                    fillcolor="rgba(255,0,0,0.1)"
                )
    
    # 添加延期标记
    if show_delays:
        current_time = datetime.now()
        for _, task in df_tasks.iterrows():
            if task['end'] < current_time and task.get('status') != '已完成':
                fig.add_annotation(
                    x=task['end'],
                    y=task[group_col],
                    text="⚠️",
                    showarrow=False,
                    font=dict(size=16, color="red")
                )
    
    return apply_plotly_theme(fig)


def create_resource_utilization_chart(
    utilization_data: Dict[str, List[float]],
    height: int = 400
) -> go.Figure:
    """创建资源利用率图表"""
    
    if not utilization_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无利用率数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)
    
    fig = go.Figure()
    
    # 为每个资源添加折线
    for resource, utilization in utilization_data.items():
        fig.add_trace(go.Scatter(
            x=list(range(len(utilization))),
            y=utilization,
            mode='lines+markers',
            name=resource,
            line=dict(width=3),
            marker=dict(size=8)
        ))
    
    # 添加平均利用率线
    all_values = [val for values in utilization_data.values() for val in values]
    avg_utilization = sum(all_values) / len(all_values)
    
    fig.add_hline(
        y=avg_utilization,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"平均利用率: {avg_utilization:.1f}%"
    )
    
    fig.update_layout(
        title="资源利用率趋势",
        xaxis_title="时间段",
        yaxis_title="利用率 (%)",
        height=height,
        yaxis=dict(range=[0, 100])
    )
    
    return apply_plotly_theme(fig)


def create_production_dashboard(
    production_data: Dict[str, Any],
    height: int = 800
) -> go.Figure:
    """创建生产仪表盘"""
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=("生产进度", "设备状态", "质量指标", "效率趋势"),
        specs=[
            [{"type": "indicator"}, {"type": "pie"}],
            [{"type": "bar"}, {"type": "scatter"}]
        ]
    )
    
    # 生产进度指示器
    progress = production_data.get("progress", 0)
    fig.add_trace(
        go.Indicator(
            mode="gauge+number+delta",
            value=progress,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "完成进度 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ),
        row=1, col=1
    )
    
    # 设备状态饼图
    equipment_status = production_data.get("equipment_status", {})
    if equipment_status:
        fig.add_trace(
            go.Pie(
                labels=list(equipment_status.keys()),
                values=list(equipment_status.values()),
                name="设备状态"
            ),
            row=1, col=2
        )
    
    # 质量指标柱状图
    quality_metrics = production_data.get("quality_metrics", {})
    if quality_metrics:
        fig.add_trace(
            go.Bar(
                x=list(quality_metrics.keys()),
                y=list(quality_metrics.values()),
                name="质量指标",
                marker_color="green"
            ),
            row=2, col=1
        )
    
    # 效率趋势线图
    efficiency_trend = production_data.get("efficiency_trend", {})
    if efficiency_trend:
        fig.add_trace(
            go.Scatter(
                x=efficiency_trend.get("dates", []),
                y=efficiency_trend.get("values", []),
                mode='lines+markers',
                name="效率趋势",
                line=dict(color="blue", width=3)
            ),
            row=2, col=2
        )
    
    fig.update_layout(
        height=height,
        title_text="生产监控仪表盘",
        showlegend=False
    )
    
    return apply_plotly_theme(fig)


def create_equipment_oee_chart(
    equipment_data: List[Dict[str, Any]],
    height: int = 400
) -> go.Figure:
    """创建设备OEE图表"""
    
    if not equipment_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无设备数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)
    
    df = pd.DataFrame(equipment_data)
    
    # 创建OEE分解图
    fig = go.Figure()
    
    # 可用率
    fig.add_trace(go.Bar(
        name='可用率',
        x=df['name'],
        y=df.get('availability', [85] * len(df)),
        marker_color='lightblue'
    ))
    
    # 性能率
    fig.add_trace(go.Bar(
        name='性能率',
        x=df['name'],
        y=df.get('performance', [90] * len(df)),
        marker_color='lightgreen'
    ))
    
    # 质量率
    fig.add_trace(go.Bar(
        name='质量率',
        x=df['name'],
        y=df.get('quality', [95] * len(df)),
        marker_color='lightyellow'
    ))
    
    # OEE总值
    fig.add_trace(go.Scatter(
        name='OEE',
        x=df['name'],
        y=df.get('oee', [72] * len(df)),
        mode='lines+markers',
        line=dict(color='red', width=3),
        marker=dict(size=10),
        yaxis='y2'
    ))
    
    fig.update_layout(
        title='设备OEE分析',
        xaxis_title='设备',
        yaxis_title='百分比 (%)',
        yaxis2=dict(
            title='OEE (%)',
            overlaying='y',
            side='right'
        ),
        height=height,
        barmode='group'
    )
    
    return apply_plotly_theme(fig)


def create_order_status_chart(
    order_data: List[Dict[str, Any]],
    chart_type: str = "pie",
    height: int = 400
) -> go.Figure:
    """创建订单状态图表"""
    
    if not order_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无订单数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)
    
    # 统计订单状态
    status_counts = {}
    for order in order_data:
        status = order.get('status', '未知')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    if chart_type == "pie":
        fig = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="订单状态分布"
        )
    else:  # bar
        fig = px.bar(
            x=list(status_counts.keys()),
            y=list(status_counts.values()),
            title="订单状态统计",
            color=list(status_counts.keys())
        )
    
    fig.update_layout(height=height)
    
    return apply_plotly_theme(fig)


def create_capacity_analysis_chart(
    capacity_data: Dict[str, Any],
    height: int = 500
) -> go.Figure:
    """创建产能分析图表"""
    
    if not capacity_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无产能数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)
    
    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=("产能利用率", "产能缺口分析"),
        specs=[[{"secondary_y": True}, {"type": "bar"}]]
    )
    
    # 产能利用率
    dates = capacity_data.get("dates", [])
    planned_capacity = capacity_data.get("planned_capacity", [])
    actual_capacity = capacity_data.get("actual_capacity", [])
    
    fig.add_trace(
        go.Scatter(
            x=dates,
            y=planned_capacity,
            mode='lines',
            name='计划产能',
            line=dict(color='blue', dash='dash')
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=dates,
            y=actual_capacity,
            mode='lines+markers',
            name='实际产能',
            line=dict(color='green')
        ),
        row=1, col=1
    )
    
    # 产能缺口
    equipment_names = capacity_data.get("equipment_names", [])
    capacity_gaps = capacity_data.get("capacity_gaps", [])
    
    fig.add_trace(
        go.Bar(
            x=equipment_names,
            y=capacity_gaps,
            name='产能缺口',
            marker_color=['red' if gap > 0 else 'green' for gap in capacity_gaps]
        ),
        row=1, col=2
    )
    
    fig.update_layout(
        height=height,
        title_text="产能分析"
    )
    
    return apply_plotly_theme(fig)


def create_kpi_indicators(
    kpi_data: Dict[str, Any],
    layout: str = "horizontal"
) -> go.Figure:
    """创建KPI指标图表"""
    
    if not kpi_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无KPI数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)
    
    kpis = kpi_data.get("kpis", [])
    
    if layout == "horizontal":
        cols = len(kpis)
        rows = 1
    else:
        cols = 2
        rows = (len(kpis) + 1) // 2
    
    fig = make_subplots(
        rows=rows,
        cols=cols,
        specs=[[{"type": "indicator"}] * cols] * rows,
        subplot_titles=[kpi["name"] for kpi in kpis]
    )
    
    for i, kpi in enumerate(kpis):
        row = i // cols + 1
        col = i % cols + 1
        
        fig.add_trace(
            go.Indicator(
                mode="number+delta+gauge",
                value=kpi["value"],
                delta={"reference": kpi.get("target", kpi["value"])},
                gauge={
                    "axis": {"range": [0, kpi.get("max_value", 100)]},
                    "bar": {"color": kpi.get("color", "darkblue")},
                    "steps": [
                        {"range": [0, kpi.get("target", 50)], "color": "lightgray"},
                    ],
                    "threshold": {
                        "line": {"color": "red", "width": 4},
                        "thickness": 0.75,
                        "value": kpi.get("target", 80)
                    }
                },
                title={"text": kpi["name"]},
                domain={"row": row-1, "column": col-1}
            ),
            row=row, col=col
        )
    
    fig.update_layout(
        height=300 * rows,
        title_text="关键绩效指标"
    )
    
    return apply_plotly_theme(fig)
