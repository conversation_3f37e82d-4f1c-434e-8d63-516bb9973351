"""
数据集成演示页面
展示如何使用数据集成服务为LLM和算法提供数据
"""

import streamlit as st
import json
from datetime import datetime

from utils.auth import check_authentication, require_permission
from services.data_integration_service import data_integration_service

# 页面配置
st.set_page_config(
    page_title="数据集成演示 - Smart APS",
    page_icon="🔗",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 页面标题
st.title("🔗 数据集成演示")
st.markdown("### 展示系统如何整合所有数据源为LLM和算法提供完整的数据上下文")

# 侧边栏 - 数据源选择
with st.sidebar:
    st.markdown("### 📊 数据源")
    
    show_equipment = st.checkbox("设备数据", value=True)
    show_pci = st.checkbox("PCI数据", value=True)
    show_uploads = st.checkbox("上传文件数据", value=True)
    show_user_inputs = st.checkbox("用户输入数据", value=True)
    show_learning = st.checkbox("学习引擎数据", value=True)
    
    st.markdown("---")
    
    st.markdown("### ⚙️ 输出格式")
    output_format = st.selectbox(
        "选择输出格式",
        ["LLM上下文", "算法输入", "完整数据", "JSON格式"]
    )
    
    if st.button("🔄 刷新数据", use_container_width=True):
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["🤖 LLM集成", "🧮 算法集成", "📊 数据概览", "🔧 测试工具"])

with tab1:
    st.markdown("#### 🤖 LLM数据集成")
    
    st.markdown("##### 📝 LLM提示上下文")
    
    # 获取LLM上下文
    if st.button("生成LLM上下文", type="primary"):
        with st.spinner("正在整合数据..."):
            llm_context = data_integration_service.get_llm_prompt_context()
            
            st.markdown("**生成的LLM上下文：**")
            st.text_area("LLM上下文", llm_context, height=400)
            
            # 显示上下文统计
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("字符数", len(llm_context))
            
            with col2:
                st.metric("行数", len(llm_context.split('\n')))
            
            with col3:
                st.metric("数据源", "5个")
    
    st.markdown("---")
    
    st.markdown("##### 🎯 LLM使用示例")
    
    # 模拟LLM对话
    user_question = st.text_input(
        "向AI助手提问",
        placeholder="例如：当前生产线L02停线，如何调整生产计划？"
    )
    
    if user_question and st.button("🤖 获取AI回答"):
        # 获取完整数据上下文
        data_context = data_integration_service.get_comprehensive_data_context()
        
        # 模拟LLM回答（实际应用中会调用真实的LLM API）
        mock_response = f"""
基于当前系统数据分析：

**设备状态分析：**
- 生产线L02确实处于停线状态，原因：{data_context['data_sources']['equipment']['unavailable_equipment'][0].get('unavailable_reason', '原料短缺') if data_context['data_sources']['equipment']['unavailable_equipment'] else '原料短缺'}
- 可用生产线：{len(data_context['data_sources']['equipment']['available_equipment'])}条
- 总产能影响：约{45}件/小时的产能损失

**建议调整方案：**
1. 将L02的订单转移到L01或L04生产线
2. 优先处理PCI库存中的老旧物料（超过180天的有{data_context['data_sources']['pci']['analysis']['old_items_count']}个）
3. 调整生产排程，延长其他生产线的工作时间

**预期影响：**
- 总体延期：1-2天
- 成本增加：约5-8%
- 建议监控L02恢复时间，及时调整计划
        """
        
        st.markdown("**AI助手回答：**")
        st.markdown(mock_response)
        
        # 记录用户问题
        data_integration_service.record_user_input(
            input_type="llm_question",
            input_data={"question": user_question, "timestamp": datetime.now().isoformat()},
            source_page="11_数据集成演示",
            user_id=st.session_state.get("user_id")
        )

with tab2:
    st.markdown("#### 🧮 算法数据集成")
    
    st.markdown("##### 📊 算法输入数据")
    
    if st.button("生成算法输入数据", type="primary"):
        with st.spinner("正在准备算法数据..."):
            algorithm_data = data_integration_service.get_algorithm_input_data()
            
            # 显示数据结构
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**设备数据：**")
                equipment_data = algorithm_data.get("equipment", {})
                st.json({
                    "available_capacity": equipment_data.get("available_capacity", {}),
                    "constraints_count": len(equipment_data.get("constraints", []))
                })
                
                st.markdown("**物料数据：**")
                materials_data = algorithm_data.get("materials", {})
                st.json({
                    "fs_inventory_count": len(materials_data.get("fs_inventory", [])),
                    "high_priority_count": len([p for p in materials_data.get("consumption_priorities", []) if p.get("priority", 0) > 80])
                })
            
            with col2:
                st.markdown("**历史数据：**")
                historical_data = algorithm_data.get("historical_data", {})
                st.json({
                    "learning_enabled": historical_data.get("learning_status", {}).get("enabled", False),
                    "sample_count": historical_data.get("learning_status", {}).get("total_samples", 0),
                    "uploaded_files": len(historical_data.get("uploaded_data", []))
                })
                
                st.markdown("**优化目标：**")
                st.json(algorithm_data.get("optimization_targets", {}))
    
    st.markdown("---")
    
    st.markdown("##### 🎯 算法使用示例")
    
    # 算法参数配置
    col1, col2 = st.columns(2)
    
    with col1:
        algorithm_type = st.selectbox(
            "算法类型",
            ["遗传算法", "模拟退火", "粒子群优化", "混合算法"]
        )
        
        max_iterations = st.number_input("最大迭代次数", 100, 10000, 1000)
    
    with col2:
        optimization_target = st.selectbox(
            "优化目标",
            ["最小化完工时间", "最大化设备利用率", "最小化成本", "平衡多目标"]
        )
        
        time_limit = st.number_input("时间限制(分钟)", 1, 60, 10)
    
    if st.button("🚀 运行算法优化"):
        # 记录算法参数
        algorithm_params = {
            "algorithm_type": algorithm_type,
            "max_iterations": max_iterations,
            "optimization_target": optimization_target,
            "time_limit": time_limit,
            "timestamp": datetime.now().isoformat()
        }
        
        data_integration_service.record_user_input(
            input_type="algorithm_optimization",
            input_data=algorithm_params,
            source_page="11_数据集成演示",
            user_id=st.session_state.get("user_id")
        )
        
        # 模拟算法运行
        with st.spinner(f"正在运行{algorithm_type}优化..."):
            import time
            time.sleep(2)  # 模拟计算时间
            
            st.success("算法优化完成！")
            
            # 显示优化结果
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("优化改进", "15.3%", delta="↑2.1%")
            
            with col2:
                st.metric("计算时间", "1.8秒")
            
            with col3:
                st.metric("迭代次数", "456")

with tab3:
    st.markdown("#### 📊 数据概览")
    
    # 获取完整数据上下文
    if st.button("加载完整数据概览", type="primary"):
        with st.spinner("正在加载数据..."):
            data_context = data_integration_service.get_comprehensive_data_context()
            
            # 数据摘要
            st.markdown("##### 📋 数据摘要")
            
            summary = data_context.get("summary", {})
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**设备状态**")
                equipment_summary = summary.get("equipment_status", {})
                st.metric("总设备", equipment_summary.get("total", 0))
                st.metric("可用设备", equipment_summary.get("available", 0))
                st.metric("不可用设备", equipment_summary.get("unavailable", 0))
            
            with col2:
                st.markdown("**PCI状态**")
                pci_summary = summary.get("pci_status", {})
                st.metric("FS物料总数", pci_summary.get("total_fs_items", 0))
                st.metric("超龄物料", pci_summary.get("old_items", 0))
                st.metric("高优先级", pci_summary.get("high_priority", 0))
            
            with col3:
                st.markdown("**数据文件**")
                files_summary = summary.get("data_files", {})
                st.metric("最近文件", files_summary.get("recent_files", 0))
                file_types = files_summary.get("file_types", [])
                st.write(f"文件类型: {', '.join(file_types) if file_types else '无'}")
            
            # 约束条件
            st.markdown("##### ⚠️ 约束条件")
            constraints = data_context.get("constraints", [])
            
            if constraints:
                for i, constraint in enumerate(constraints):
                    st.warning(f"**约束 {i+1}**: {constraint.get('description', constraint.get('reason', '未知约束'))}")
            else:
                st.info("当前无约束条件")
            
            # 建议
            st.markdown("##### 💡 系统建议")
            recommendations = data_context.get("recommendations", [])
            
            if recommendations:
                for i, recommendation in enumerate(recommendations):
                    st.info(f"**建议 {i+1}**: {recommendation}")
            else:
                st.info("当前无特殊建议")

with tab4:
    st.markdown("#### 🔧 测试工具")
    
    st.markdown("##### 📝 手动数据录入测试")
    
    # 测试用户输入记录
    with st.form("test_input_form"):
        input_type = st.selectbox(
            "输入类型",
            ["production_plan", "equipment_config", "user_preference", "system_setting"]
        )
        
        test_data = st.text_area(
            "测试数据（JSON格式）",
            value='{"test": "data", "timestamp": "2024-01-16"}',
            height=100
        )
        
        if st.form_submit_button("📝 记录测试数据"):
            try:
                parsed_data = json.loads(test_data)
                data_integration_service.record_user_input(
                    input_type=f"test_{input_type}",
                    input_data=parsed_data,
                    source_page="11_数据集成演示",
                    user_id=st.session_state.get("user_id", "test_user")
                )
                st.success("测试数据已记录")
            except json.JSONDecodeError:
                st.error("JSON格式错误")
    
    st.markdown("---")
    
    st.markdown("##### 🔍 数据查询测试")
    
    # 查询最近的用户输入
    if st.button("查询最近用户输入"):
        data_context = data_integration_service.get_comprehensive_data_context()
        user_inputs = data_context.get("data_sources", {}).get("user_inputs", {})
        
        st.markdown("**最近用户输入：**")
        recent_inputs = user_inputs.get("recent_inputs", [])
        
        if recent_inputs:
            for i, inp in enumerate(recent_inputs[:5]):  # 显示最近5条
                with st.expander(f"输入 {i+1}: {inp.get('type', '未知类型')}"):
                    st.json(inp)
        else:
            st.info("暂无用户输入记录")
    
    # 数据源连接测试
    st.markdown("##### 🔗 数据源连接测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("测试设备数据服务"):
            try:
                equipment_data = data_integration_service.equipment_service.get_equipment_status_summary()
                st.success(f"设备数据服务正常 - 总设备数: {equipment_data.get('total', 0)}")
            except Exception as e:
                st.error(f"设备数据服务异常: {str(e)}")
    
    with col2:
        if st.button("测试PCI数据服务"):
            try:
                pci_data = data_integration_service.pci_service.get_fs_data_with_fifo(180)
                st.success(f"PCI数据服务正常 - FS物料数: {len(pci_data)}")
            except Exception as e:
                st.error(f"PCI数据服务异常: {str(e)}")
