# 智能工厂生产管理规划系统 - 扩展与插件开发

## 插件系统架构

### 设计理念

插件系统采用**事件驱动**和**依赖注入**的设计模式，提供灵活的扩展机制：

- **松耦合**：插件与核心系统通过标准接口交互
- **热插拔**：支持运行时加载和卸载插件
- **安全隔离**：插件运行在受控环境中
- **版本管理**：支持插件版本控制和依赖管理

### 插件类型

#### 1. 数据源插件
扩展系统的数据获取能力，支持新的数据源类型。

**应用场景**：
- ERP系统集成
- IoT设备数据采集
- 第三方API接入
- 自定义文件格式解析

#### 2. 算法插件
扩展优化算法和计算能力。

**应用场景**：
- 自定义优化算法
- 机器学习模型
- 启发式算法
- 仿真模拟器

#### 3. 可视化插件
扩展数据展示和交互能力。

**应用场景**：
- 自定义图表类型
- 3D可视化
- 实时监控面板
- 移动端组件

#### 4. 业务规则插件
扩展业务逻辑和规则引擎。

**应用场景**：
- 行业特定规则
- 自定义约束条件
- 工作流引擎
- 审批流程

#### 5. 通知插件
扩展消息通知和集成能力。

**应用场景**：
- 企业微信/钉钉集成
- 邮件模板定制
- SMS通知
- Webhook集成

## 插件开发指南

### 1. 开发环境准备

#### 插件开发工具包
```bash
# 安装插件开发SDK
pip install smart-aps-plugin-sdk

# 创建插件项目
aps-plugin create my-plugin --type=algorithm

# 项目结构
my-plugin/
├── plugin.yaml           # 插件配置文件
├── src/
│   ├── __init__.py
│   ├── main.py          # 插件主入口
│   └── algorithm.py     # 算法实现
├── tests/
│   └── test_algorithm.py
├── docs/
│   └── README.md
└── requirements.txt
```

#### 插件配置文件
```yaml
# plugin.yaml
name: "my-custom-algorithm"
version: "1.0.0"
description: "自定义优化算法插件"
author: "开发者姓名"
license: "MIT"

# 插件类型和入口
type: "algorithm"
entry_point: "src.main:MyAlgorithmPlugin"

# 依赖声明
dependencies:
  - "numpy>=1.21.0"
  - "scipy>=1.7.0"

# 系统要求
system_requirements:
  min_aps_version: "1.0.0"
  python_version: ">=3.9"

# 权限声明
permissions:
  - "data.read"
  - "calculation.execute"

# 配置参数
config_schema:
  type: "object"
  properties:
    max_iterations:
      type: "integer"
      default: 1000
      description: "最大迭代次数"
    tolerance:
      type: "number"
      default: 0.001
      description: "收敛容差"
```

### 2. 算法插件开发

#### 插件基类实现
```python
# src/main.py
from typing import Dict, Any, List
from smart_aps_sdk import AlgorithmPlugin, OptimizationResult
from smart_aps_sdk.types import ProblemData, Constraint

class MyAlgorithmPlugin(AlgorithmPlugin):
    """自定义算法插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "my-custom-algorithm"
        self.version = "1.0.0"
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        self.max_iterations = config.get("max_iterations", 1000)
        self.tolerance = config.get("tolerance", 0.001)
        self.logger.info(f"算法插件初始化完成，参数：{config}")
        return True
    
    async def solve(self, 
                   problem_data: ProblemData,
                   constraints: List[Constraint],
                   objectives: Dict[str, float]) -> OptimizationResult:
        """求解优化问题"""
        try:
            self.logger.info("开始执行自定义算法求解")
            
            # 数据预处理
            processed_data = await self._preprocess_data(problem_data)
            
            # 执行算法
            solution = await self._execute_algorithm(
                processed_data, constraints, objectives
            )
            
            # 结果后处理
            result = await self._postprocess_solution(solution)
            
            return OptimizationResult(
                status="optimal",
                objective_value=result["objective_value"],
                solution=result["solution"],
                solve_time=result["solve_time"],
                metadata=result.get("metadata", {})
            )
            
        except Exception as e:
            self.logger.error(f"算法执行失败: {str(e)}")
            return OptimizationResult(
                status="failed",
                error_message=str(e)
            )
    
    async def _preprocess_data(self, data: ProblemData) -> Dict[str, Any]:
        """数据预处理"""
        # 实现数据预处理逻辑
        return {
            "orders": data.orders,
            "equipment": data.equipment,
            "products": data.products,
            "time_horizon": data.time_horizon
        }
    
    async def _execute_algorithm(self, 
                                data: Dict[str, Any],
                                constraints: List[Constraint],
                                objectives: Dict[str, float]) -> Dict[str, Any]:
        """执行核心算法"""
        import time
        start_time = time.time()
        
        # 这里实现具体的算法逻辑
        # 示例：简单的贪心算法
        solution = self._greedy_algorithm(data, constraints)
        
        solve_time = time.time() - start_time
        
        return {
            "solution": solution,
            "objective_value": self._calculate_objective(solution, objectives),
            "solve_time": solve_time,
            "iterations": 100  # 示例值
        }
    
    def _greedy_algorithm(self, data: Dict[str, Any], 
                         constraints: List[Constraint]) -> Dict[str, Any]:
        """贪心算法实现"""
        # 实现贪心算法逻辑
        schedule = {}
        
        # 按优先级排序订单
        orders = sorted(data["orders"], key=lambda x: x.get("priority", 3))
        
        for order in orders:
            # 为每个订单分配设备和时间
            best_equipment = self._find_best_equipment(order, data["equipment"])
            if best_equipment:
                schedule[order["id"]] = {
                    "equipment_id": best_equipment["id"],
                    "start_time": self._calculate_start_time(best_equipment),
                    "duration": order["processing_time"]
                }
        
        return {"schedule": schedule}
    
    def _find_best_equipment(self, order: Dict[str, Any], 
                           equipment_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """寻找最佳设备"""
        # 实现设备选择逻辑
        suitable_equipment = [
            eq for eq in equipment_list 
            if eq["type"] == order["required_equipment_type"]
        ]
        
        if suitable_equipment:
            # 选择利用率最低的设备
            return min(suitable_equipment, key=lambda x: x.get("utilization", 0))
        
        return None
    
    def _calculate_start_time(self, equipment: Dict[str, Any]) -> float:
        """计算开始时间"""
        # 实现时间计算逻辑
        return equipment.get("next_available_time", 0)
    
    def _calculate_objective(self, solution: Dict[str, Any], 
                           objectives: Dict[str, float]) -> float:
        """计算目标函数值"""
        # 实现目标函数计算
        schedule = solution.get("schedule", {})
        
        # 计算makespan
        makespan = max([
            task["start_time"] + task["duration"] 
            for task in schedule.values()
        ]) if schedule else 0
        
        # 计算设备利用率
        utilization = self._calculate_utilization(schedule)
        
        # 加权目标函数
        objective_value = (
            objectives.get("minimize_makespan", 0) * makespan +
            objectives.get("maximize_utilization", 0) * (1 - utilization)
        )
        
        return objective_value
    
    def _calculate_utilization(self, schedule: Dict[str, Any]) -> float:
        """计算设备利用率"""
        # 实现利用率计算逻辑
        if not schedule:
            return 0.0
        
        total_processing_time = sum([
            task["duration"] for task in schedule.values()
        ])
        
        makespan = max([
            task["start_time"] + task["duration"] 
            for task in schedule.values()
        ])
        
        # 假设有3台设备
        total_available_time = makespan * 3
        
        return total_processing_time / total_available_time if total_available_time > 0 else 0
    
    async def _postprocess_solution(self, solution: Dict[str, Any]) -> Dict[str, Any]:
        """结果后处理"""
        # 添加额外的结果信息
        solution["metadata"] = {
            "algorithm_name": self.name,
            "algorithm_version": self.version,
            "solution_quality": "good"  # 可以添加解质量评估
        }
        
        return solution
    
    def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式"""
        return {
            "type": "object",
            "properties": {
                "max_iterations": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10000,
                    "default": 1000,
                    "description": "最大迭代次数"
                },
                "tolerance": {
                    "type": "number",
                    "minimum": 0.0001,
                    "maximum": 0.1,
                    "default": 0.001,
                    "description": "收敛容差"
                }
            }
        }
```

### 3. 数据源插件开发

#### 数据源插件示例
```python
# src/data_source.py
from typing import Dict, Any, List
import pandas as pd
from smart_aps_sdk import DataSourcePlugin
from smart_aps_sdk.types import DataSourceConfig, ValidationResult

class ERPDataSourcePlugin(DataSourcePlugin):
    """ERP系统数据源插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "erp-data-source"
        self.connection = None
    
    async def initialize(self, config: DataSourceConfig) -> bool:
        """初始化数据源连接"""
        try:
            self.api_url = config.get("api_url")
            self.api_key = config.get("api_key")
            self.timeout = config.get("timeout", 30)
            
            # 测试连接
            await self._test_connection()
            
            self.logger.info("ERP数据源初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"ERP数据源初始化失败: {str(e)}")
            return False
    
    async def extract_data(self, query: Dict[str, Any]) -> pd.DataFrame:
        """提取数据"""
        try:
            data_type = query.get("data_type")
            date_range = query.get("date_range")
            
            if data_type == "orders":
                return await self._extract_orders(date_range)
            elif data_type == "inventory":
                return await self._extract_inventory(date_range)
            elif data_type == "equipment":
                return await self._extract_equipment()
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
                
        except Exception as e:
            self.logger.error(f"数据提取失败: {str(e)}")
            raise
    
    async def validate_data(self, data: pd.DataFrame, 
                          data_type: str) -> ValidationResult:
        """验证数据"""
        errors = []
        warnings = []
        
        # 基础验证
        if data.empty:
            errors.append("数据为空")
            return ValidationResult(is_valid=False, errors=errors)
        
        # 根据数据类型进行特定验证
        if data_type == "orders":
            errors.extend(self._validate_orders(data))
        elif data_type == "inventory":
            errors.extend(self._validate_inventory(data))
        elif data_type == "equipment":
            errors.extend(self._validate_equipment(data))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    async def _test_connection(self):
        """测试连接"""
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            async with session.get(
                f"{self.api_url}/health",
                headers=headers,
                timeout=self.timeout
            ) as response:
                if response.status != 200:
                    raise ConnectionError(f"连接失败，状态码: {response.status}")
    
    async def _extract_orders(self, date_range: Dict[str, str]) -> pd.DataFrame:
        """提取订单数据"""
        import aiohttp
        
        params = {
            "start_date": date_range["start_date"],
            "end_date": date_range["end_date"]
        }
        
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            async with session.get(
                f"{self.api_url}/orders",
                headers=headers,
                params=params,
                timeout=self.timeout
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return pd.DataFrame(data["orders"])
                else:
                    raise Exception(f"API请求失败: {response.status}")
    
    def _validate_orders(self, data: pd.DataFrame) -> List[str]:
        """验证订单数据"""
        errors = []
        
        required_columns = ["order_id", "product_id", "quantity", "due_date"]
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查数据类型
        if "quantity" in data.columns:
            if not pd.api.types.is_numeric_dtype(data["quantity"]):
                errors.append("quantity列必须为数值类型")
        
        # 检查空值
        null_counts = data.isnull().sum()
        for col, count in null_counts.items():
            if count > 0 and col in required_columns:
                errors.append(f"列{col}存在{count}个空值")
        
        return errors
```

### 4. 可视化插件开发

#### 图表插件示例
```python
# src/visualization.py
from typing import Dict, Any, List
import plotly.graph_objects as go
from smart_aps_sdk import VisualizationPlugin

class GanttChartPlugin(VisualizationPlugin):
    """甘特图可视化插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "advanced-gantt-chart"
    
    async def render(self, data: Dict[str, Any], 
                    config: Dict[str, Any]) -> Dict[str, Any]:
        """渲染图表"""
        try:
            # 解析数据
            schedule_data = data.get("schedule", {})
            equipment_data = data.get("equipment", [])
            
            # 创建甘特图
            fig = self._create_gantt_chart(schedule_data, equipment_data, config)
            
            # 返回图表配置
            return {
                "type": "plotly",
                "config": fig.to_dict(),
                "metadata": {
                    "title": config.get("title", "生产计划甘特图"),
                    "description": "显示设备生产计划的时间安排"
                }
            }
            
        except Exception as e:
            self.logger.error(f"图表渲染失败: {str(e)}")
            raise
    
    def _create_gantt_chart(self, schedule_data: Dict[str, Any],
                           equipment_data: List[Dict[str, Any]],
                           config: Dict[str, Any]) -> go.Figure:
        """创建甘特图"""
        fig = go.Figure()
        
        # 设备颜色映射
        colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7"]
        equipment_colors = {
            eq["id"]: colors[i % len(colors)] 
            for i, eq in enumerate(equipment_data)
        }
        
        # 添加任务条
        for task_id, task_info in schedule_data.items():
            equipment_id = task_info["equipment_id"]
            start_time = task_info["start_time"]
            duration = task_info["duration"]
            
            fig.add_trace(go.Scatter(
                x=[start_time, start_time + duration],
                y=[equipment_id, equipment_id],
                mode='lines',
                line=dict(
                    color=equipment_colors.get(equipment_id, "#95A5A6"),
                    width=20
                ),
                name=f"任务 {task_id}",
                hovertemplate=(
                    f"任务: {task_id}<br>"
                    f"设备: {equipment_id}<br>"
                    f"开始时间: {start_time}<br>"
                    f"持续时间: {duration}<br>"
                    "<extra></extra>"
                )
            ))
        
        # 更新布局
        fig.update_layout(
            title=config.get("title", "生产计划甘特图"),
            xaxis_title="时间",
            yaxis_title="设备",
            showlegend=True,
            height=600,
            hovermode='closest'
        )
        
        return fig
    
    def get_config_schema(self) -> Dict[str, Any]:
        """获取配置模式"""
        return {
            "type": "object",
            "properties": {
                "title": {
                    "type": "string",
                    "default": "生产计划甘特图",
                    "description": "图表标题"
                },
                "show_legend": {
                    "type": "boolean",
                    "default": True,
                    "description": "是否显示图例"
                },
                "height": {
                    "type": "integer",
                    "default": 600,
                    "description": "图表高度"
                }
            }
        }
```

### 5. 插件测试

#### 单元测试示例
```python
# tests/test_algorithm.py
import pytest
import asyncio
from src.main import MyAlgorithmPlugin
from smart_aps_sdk.types import ProblemData, Constraint

class TestMyAlgorithmPlugin:
    
    @pytest.fixture
    def plugin(self):
        return MyAlgorithmPlugin()
    
    @pytest.fixture
    def sample_data(self):
        return ProblemData(
            orders=[
                {"id": "order_1", "product_id": "prod_1", "quantity": 100, "priority": 1},
                {"id": "order_2", "product_id": "prod_2", "quantity": 50, "priority": 2}
            ],
            equipment=[
                {"id": "eq_1", "type": "TANK", "capacity": 200, "utilization": 0.5},
                {"id": "eq_2", "type": "FINISHING", "capacity": 100, "utilization": 0.3}
            ],
            products=[
                {"id": "prod_1", "name": "产品1", "processing_time": 2.0},
                {"id": "prod_2", "name": "产品2", "processing_time": 1.5}
            ],
            time_horizon=30
        )
    
    @pytest.mark.asyncio
    async def test_plugin_initialization(self, plugin):
        """测试插件初始化"""
        config = {"max_iterations": 500, "tolerance": 0.01}
        result = await plugin.initialize(config)
        
        assert result is True
        assert plugin.max_iterations == 500
        assert plugin.tolerance == 0.01
    
    @pytest.mark.asyncio
    async def test_solve_algorithm(self, plugin, sample_data):
        """测试算法求解"""
        await plugin.initialize({"max_iterations": 100})
        
        constraints = [
            Constraint(type="capacity", equipment_id="eq_1", max_utilization=0.9)
        ]
        objectives = {"minimize_makespan": 0.6, "maximize_utilization": 0.4}
        
        result = await plugin.solve(sample_data, constraints, objectives)
        
        assert result.status == "optimal"
        assert result.objective_value is not None
        assert result.solution is not None
        assert "schedule" in result.solution
    
    def test_config_schema(self, plugin):
        """测试配置模式"""
        schema = plugin.get_config_schema()
        
        assert "type" in schema
        assert schema["type"] == "object"
        assert "properties" in schema
        assert "max_iterations" in schema["properties"]
        assert "tolerance" in schema["properties"]
```

### 6. 插件打包与发布

#### 打包脚本
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="my-custom-algorithm-plugin",
    version="1.0.0",
    description="自定义优化算法插件",
    author="开发者姓名",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "smart-aps-plugin-sdk>=1.0.0",
        "numpy>=1.21.0",
        "scipy>=1.7.0"
    ],
    entry_points={
        "smart_aps.plugins": [
            "my-algorithm = src.main:MyAlgorithmPlugin"
        ]
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.9",
)
```

#### 发布流程
```bash
# 1. 运行测试
pytest tests/

# 2. 构建插件包
python setup.py sdist bdist_wheel

# 3. 验证插件
aps-plugin validate dist/my-custom-algorithm-plugin-1.0.0.tar.gz

# 4. 发布到插件仓库
aps-plugin publish dist/my-custom-algorithm-plugin-1.0.0.tar.gz
```

## 插件管理

### 1. 插件安装与配置

#### 通过Web界面管理
```python
# 插件管理API
@router.post("/plugins/install")
async def install_plugin(plugin_file: UploadFile, 
                        current_user: User = Depends(get_current_user)):
    """安装插件"""
    # 验证用户权限
    if not current_user.has_permission("plugin.install"):
        raise HTTPException(403, "权限不足")
    
    # 保存插件文件
    plugin_path = await save_plugin_file(plugin_file)
    
    # 验证插件
    validation_result = await plugin_manager.validate_plugin(plugin_path)
    if not validation_result.is_valid:
        raise HTTPException(400, f"插件验证失败: {validation_result.errors}")
    
    # 安装插件
    install_result = await plugin_manager.install_plugin(plugin_path)
    
    return {
        "success": True,
        "plugin_id": install_result.plugin_id,
        "message": "插件安装成功"
    }

@router.get("/plugins")
async def list_plugins(current_user: User = Depends(get_current_user)):
    """获取插件列表"""
    plugins = await plugin_manager.list_plugins()
    
    return {
        "success": True,
        "data": [
            {
                "id": plugin.id,
                "name": plugin.name,
                "version": plugin.version,
                "status": plugin.status,
                "description": plugin.description,
                "author": plugin.author
            }
            for plugin in plugins
        ]
    }
```

### 2. 插件安全与权限

#### 权限控制
```python
class PluginPermissionManager:
    """插件权限管理器"""
    
    def __init__(self):
        self.permissions = {
            "data.read": "读取数据",
            "data.write": "写入数据", 
            "calculation.execute": "执行计算",
            "system.config": "系统配置",
            "network.access": "网络访问"
        }
    
    def validate_permissions(self, plugin_manifest: Dict[str, Any]) -> bool:
        """验证插件权限"""
        requested_permissions = plugin_manifest.get("permissions", [])
        
        # 检查是否请求了危险权限
        dangerous_permissions = ["system.config", "network.access"]
        for perm in requested_permissions:
            if perm in dangerous_permissions:
                # 需要管理员审批
                return self._require_admin_approval(plugin_manifest, perm)
        
        return True
    
    def _require_admin_approval(self, plugin_manifest: Dict[str, Any], 
                               permission: str) -> bool:
        """需要管理员审批"""
        # 实现审批流程
        approval_request = {
            "plugin_name": plugin_manifest["name"],
            "permission": permission,
            "reason": f"插件请求{permission}权限",
            "status": "pending"
        }
        
        # 发送审批通知
        self._send_approval_notification(approval_request)
        
        return False  # 暂时拒绝，等待审批
```

这个扩展与插件开发文档提供了完整的插件系统架构、开发指南、测试方法和管理机制。
