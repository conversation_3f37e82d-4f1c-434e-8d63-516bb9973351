#!/bin/bash

# Smart APS Backend 启动脚本

set -e

echo "🚀 Smart APS Backend 启动中..."

# 等待数据库就绪
echo "⏳ 等待数据库连接..."
python scripts/wait_for_db.py

# 运行数据库迁移
echo "🔄 执行数据库迁移..."
python scripts/init_database.py

# 初始化基础数据
echo "📊 初始化基础数据..."
python scripts/init_data.py

# 启动应用
echo "✅ 启动FastAPI应用..."
if [ "$DEBUG" = "true" ]; then
    echo "🔧 开发模式启动"
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
else
    echo "🏭 生产模式启动"
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
fi
