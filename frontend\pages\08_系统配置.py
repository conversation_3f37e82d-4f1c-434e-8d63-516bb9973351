"""
系统配置页面
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import json
import plotly.express as px
import plotly.graph_objects as go
import random

from config.settings import AppConfig
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="系统配置 - Smart APS",
    page_icon="⚙️",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("admin.config"):
    st.error("权限不足，需要系统配置权限")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 初始化会话状态
if 'config_changed' not in st.session_state:
    st.session_state.config_changed = False

if 'show_backup_modal' not in st.session_state:
    st.session_state.show_backup_modal = False

if 'show_import_modal' not in st.session_state:
    st.session_state.show_import_modal = False

if 'selected_backup_id' not in st.session_state:
    st.session_state.selected_backup_id = None

if 'original_config' not in st.session_state:
    st.session_state.original_config = {}

if 'confirm_reset' not in st.session_state:
    st.session_state.confirm_reset = False

# 配置类型定义
CONFIG_TYPES = {
    "system": {
        "name": "系统参数",
        "icon": "⚙️",
        "color": "#FF6B6B",
        "description": "基础配置、性能参数、安全设置、日志配置"
    },
    "business": {
        "name": "业务配置",
        "icon": "🏭",
        "color": "#4ECDC4",
        "description": "生产参数、质量标准、成本配置、工艺参数"
    },
    "integration": {
        "name": "集成配置",
        "icon": "🔗",
        "color": "#45B7D1",
        "description": "API配置、数据库连接、第三方集成、通知设置"
    },
    "interface": {
        "name": "界面配置",
        "icon": "🎨",
        "color": "#96CEB4",
        "description": "主题设置、菜单配置、仪表板配置、多语言支持"
    },
    "monitoring": {
        "name": "监控配置",
        "icon": "📊",
        "color": "#FFEAA7",
        "description": "性能监控、业务监控、用户行为、系统健康"
    }
}

# 页面标题
st.title("⚙️ 系统配置")
st.markdown("### 管理系统参数、业务配置和集成设置")

# 侧边栏 - 配置管理
with st.sidebar:
    st.markdown("### 🛠️ 配置管理")

    # 配置类型选择
    selected_config_type = st.selectbox(
        "配置类型",
        options=list(CONFIG_TYPES.keys()),
        format_func=lambda x: f"{CONFIG_TYPES[x]['icon']} {CONFIG_TYPES[x]['name']}",
        index=0,
        key="config_type_selector"
    )

    # 保存当前选择的配置类型到会话状态
    st.session_state.selected_config_type = selected_config_type

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("💾 保存配置", use_container_width=True):
        save_current_config()

    if st.button("🔄 重置配置", use_container_width=True):
        reset_current_config()

    if st.button("📋 创建备份", use_container_width=True):
        st.session_state.show_backup_modal = True
        st.rerun()

    if st.button("📥 导入配置", use_container_width=True):
        st.session_state.show_import_modal = True
        st.rerun()

    if st.button("📤 导出配置", use_container_width=True):
        export_current_config()

    if st.button("📋 配置模板", use_container_width=True):
        show_config_templates()

    st.markdown("---")

    # 配置说明
    st.markdown("### 📝 配置说明")

    config_info = CONFIG_TYPES[selected_config_type]
    st.markdown(f"**{config_info['icon']} {config_info['name']}**")
    st.markdown(f"<small>{config_info['description']}</small>", unsafe_allow_html=True)

    # 配置状态
    if st.session_state.config_changed:
        st.warning("⚠️ 配置已修改，请保存")
    else:
        st.success("✅ 配置已同步")

# 辅助函数定义
def get_system_config(config_type=None):
    """获取系统配置"""
    try:
        result = api_client.get_system_config(config_type)

        if not result.get("success"):
            return get_mock_system_config(config_type)

        return result
    except Exception as e:
        return get_mock_system_config(config_type)


def get_business_config(config_type=None):
    """获取业务配置"""
    try:
        result = api_client.get_business_config(config_type)

        if not result.get("success"):
            return get_mock_business_config(config_type)

        return result
    except Exception as e:
        return get_mock_business_config(config_type)


def get_integration_config(integration_type=None):
    """获取集成配置"""
    try:
        result = api_client.get_integration_config(integration_type)

        if not result.get("success"):
            return get_mock_integration_config(integration_type)

        return result
    except Exception as e:
        return get_mock_integration_config(integration_type)


def save_current_config():
    """保存当前配置"""
    try:
        # 获取当前配置类型
        config_type = st.session_state.get('selected_config_type', 'system')

        # 根据配置类型收集配置数据
        if config_type == "system":
            config_data = collect_system_config_data()
        elif config_type == "business":
            config_data = collect_business_config_data()
        elif config_type == "integration":
            config_data = collect_integration_config_data()
        else:
            # 对于界面和监控配置，暂时使用模拟数据
            config_data = {}

        # 验证配置数据
        is_valid, validation_message = validate_config_data(config_type, config_data)
        if not is_valid:
            st.error(f"配置验证失败: {validation_message}")
            return

        # 调用相应的保存API
        if config_type == "system":
            result = api_client.update_system_config(config_data)
        elif config_type == "business":
            result = api_client.update_business_config(config_data)
        elif config_type == "integration":
            result = api_client.update_integration_config(config_data)
        else:
            # 对于界面和监控配置，暂时使用模拟保存
            result = {"success": True, "message": "配置保存成功"}

        if result.get("success"):
            st.success("配置保存成功！")
            mark_config_saved()  # 标记配置已保存
            # 创建自动备份
            create_auto_backup(config_type)
        else:
            st.error(f"配置保存失败: {result.get('message', '未知错误')}")

    except Exception as e:
        st.error(f"配置保存失败: {str(e)}")


def reset_current_config():
    """重置当前配置"""
    try:
        config_type = st.session_state.get('selected_config_type', 'system')

        # 确认重置操作
        if st.session_state.get('confirm_reset', False):
            # 重置配置到默认值
            if config_type == "system":
                default_config = get_default_system_config()
                result = api_client.update_system_config(default_config)
            elif config_type == "business":
                default_config = get_default_business_config()
                result = api_client.update_business_config(default_config)
            elif config_type == "integration":
                default_config = get_default_integration_config()
                result = api_client.update_integration_config(default_config)
            else:
                result = {"success": True, "message": "配置重置成功"}

            if result.get("success"):
                st.success("配置重置成功！")
                st.session_state.config_changed = False
                st.session_state.confirm_reset = False
                st.rerun()
            else:
                st.error(f"配置重置失败: {result.get('message', '未知错误')}")
        else:
            # 显示确认对话框
            st.warning("⚠️ 确定要重置配置吗？此操作将恢复默认设置。")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ 确认重置", key="confirm_reset_btn"):
                    st.session_state.confirm_reset = True
                    st.rerun()
            with col2:
                if st.button("❌ 取消", key="cancel_reset_btn"):
                    st.session_state.confirm_reset = False

    except Exception as e:
        st.error(f"配置重置失败: {str(e)}")


def export_current_config():
    """导出当前配置"""
    try:
        config_type = st.session_state.get('selected_config_type', 'system')

        # 获取当前配置
        if config_type == "system":
            config_data = get_system_config()
        elif config_type == "business":
            config_data = get_business_config()
        elif config_type == "integration":
            config_data = get_integration_config()
        else:
            config_data = {"success": True, "data": {}}

        if config_data.get("success"):
            # 生成导出文件
            export_data = {
                "config_type": config_type,
                "export_time": datetime.now().isoformat(),
                "data": config_data.get("data", {})
            }

            # 转换为JSON字符串
            json_str = json.dumps(export_data, indent=2, ensure_ascii=False)

            # 提供下载
            st.download_button(
                label="📥 下载配置文件",
                data=json_str,
                file_name=f"{config_type}_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
            st.success("配置导出准备完成，点击上方按钮下载！")
        else:
            st.error("获取配置数据失败，无法导出")

    except Exception as e:
        st.error(f"配置导出失败: {str(e)}")


def create_auto_backup(config_type):
    """创建自动备份"""
    try:
        backup_name = f"auto_backup_{config_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        description = f"保存{CONFIG_TYPES[config_type]['name']}时的自动备份"

        result = api_client.create_config_backup(backup_name, description)
        if result.get("success"):
            st.info("✅ 已自动创建配置备份")
    except Exception as e:
        st.warning(f"自动备份创建失败: {str(e)}")


def detect_config_changes(config_type, current_config):
    """检测配置变更"""
    try:
        original_key = f"original_{config_type}_config"
        original_config = st.session_state.get(original_key, {})

        # 如果没有原始配置，保存当前配置作为原始配置
        if not original_config:
            st.session_state[original_key] = current_config.copy()
            return False

        # 比较配置
        for key, value in current_config.items():
            if original_config.get(key) != value:
                return True

        return False
    except Exception as e:
        return False


def mark_config_changed():
    """标记配置已变更"""
    st.session_state.config_changed = True


def mark_config_saved():
    """标记配置已保存"""
    st.session_state.config_changed = False
    # 更新原始配置
    config_type = st.session_state.get('selected_config_type', 'system')
    if config_type == "system":
        current_config = collect_system_config_data()
    elif config_type == "business":
        current_config = collect_business_config_data()
    elif config_type == "integration":
        current_config = collect_integration_config_data()
    else:
        current_config = {}

    original_key = f"original_{config_type}_config"
    st.session_state[original_key] = current_config.copy()


def collect_system_config_data():
    """收集系统配置数据"""
    # 这里应该从UI组件中收集实际的配置值
    # 由于Streamlit的限制，这里返回模拟数据
    return {
        "system_name": "Smart APS",
        "system_version": "1.0.0",
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "max_concurrent_users": 100,
        "session_timeout": 30
    }


def collect_business_config_data():
    """收集业务配置数据"""
    return {
        "work_hours_start": "08:00",
        "work_hours_end": "18:00",
        "shifts_per_day": 2,
        "quality_target": 99.0
    }


def collect_integration_config_data():
    """收集集成配置数据"""
    return {
        "database_host": "localhost",
        "database_port": 5432,
        "api_timeout": 30
    }


def get_default_system_config():
    """获取默认系统配置"""
    return {
        "system_name": "Smart APS",
        "system_version": "1.0.0",
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "date_format": "YYYY-MM-DD",
        "currency": "CNY",
        "max_concurrent_users": 100,
        "session_timeout": 30,
        "request_timeout": 30,
        "cache_size": 128,
        "password_min_length": 8,
        "password_require_special": True,
        "enable_2fa": False,
        "max_login_attempts": 5,
        "lockout_duration": 30,
        "enable_audit_log": True,
        "log_level": "INFO",
        "log_retention_days": 30,
        "max_log_file_size": 10,
        "enable_log_rotation": True
    }


def get_default_business_config():
    """获取默认业务配置"""
    return {
        "work_hours_start": "08:00",
        "work_hours_end": "18:00",
        "shifts_per_day": 2,
        "working_days_per_week": 5,
        "default_capacity": 1000,
        "quality_target": 99.0,
        "defect_rate_threshold": 1.0,
        "cost_calculation_method": "standard",
        "default_labor_cost": 50.0,
        "default_material_cost": 30.0,
        "process_efficiency_target": 85.0,
        "equipment_utilization_target": 80.0
    }


def get_default_integration_config():
    """获取默认集成配置"""
    return {
        "database_host": "localhost",
        "database_port": 5432,
        "database_name": "smart_aps",
        "api_timeout": 30,
        "api_retry_count": 3,
        "email_smtp_server": "smtp.example.com",
        "email_smtp_port": 587,
        "email_use_tls": True,
        "sms_provider": "aliyun",
        "erp_integration_enabled": False,
        "mes_integration_enabled": False,
        "iot_integration_enabled": True
    }


def show_config_templates():
    """显示配置模板"""
    st.markdown("### 📋 配置模板")

    # 配置模板定义
    templates = {
        "小型企业": {
            "description": "适合20-50人的小型制造企业",
            "system": {
                "max_concurrent_users": 50,
                "session_timeout": 60,
                "cache_size": 64,
                "log_retention_days": 15
            },
            "business": {
                "shifts_per_day": 1,
                "working_days_per_week": 5,
                "default_capacity": 500,
                "quality_target": 95.0
            }
        },
        "中型企业": {
            "description": "适合50-200人的中型制造企业",
            "system": {
                "max_concurrent_users": 100,
                "session_timeout": 30,
                "cache_size": 128,
                "log_retention_days": 30
            },
            "business": {
                "shifts_per_day": 2,
                "working_days_per_week": 6,
                "default_capacity": 1000,
                "quality_target": 98.0
            }
        },
        "大型企业": {
            "description": "适合200人以上的大型制造企业",
            "system": {
                "max_concurrent_users": 300,
                "session_timeout": 20,
                "cache_size": 256,
                "log_retention_days": 60
            },
            "business": {
                "shifts_per_day": 3,
                "working_days_per_week": 7,
                "default_capacity": 2000,
                "quality_target": 99.5
            }
        },
        "高安全要求": {
            "description": "适合对安全要求较高的企业",
            "system": {
                "password_min_length": 12,
                "password_require_special": True,
                "enable_2fa": True,
                "max_login_attempts": 3,
                "lockout_duration": 60,
                "enable_audit_log": True
            }
        },
        "高性能配置": {
            "description": "适合对性能要求较高的企业",
            "system": {
                "max_concurrent_users": 500,
                "request_timeout": 10,
                "cache_size": 512
            },
            "integration": {
                "api_timeout": 10,
                "api_retry_count": 5,
                "database_pool_size": 50
            }
        }
    }

    # 模板选择
    selected_template = st.selectbox(
        "选择配置模板",
        options=list(templates.keys()),
        help="选择适合您企业的配置模板"
    )

    if selected_template:
        template = templates[selected_template]

        st.markdown(f"**模板描述**: {template['description']}")

        # 显示模板配置
        st.markdown("**模板配置预览**:")

        for config_type, config_data in template.items():
            if config_type != "description":
                st.markdown(f"**{CONFIG_TYPES.get(config_type, {}).get('name', config_type)}配置**:")
                st.json(config_data)

        # 应用模板
        col1, col2 = st.columns(2)

        with col1:
            if st.button("✅ 应用模板", key=f"apply_template_{selected_template}"):
                apply_config_template(selected_template, template)

        with col2:
            if st.button("📥 下载模板", key=f"download_template_{selected_template}"):
                download_config_template(selected_template, template)


def apply_config_template(template_name, template_config):
    """应用配置模板"""
    try:
        success_count = 0
        total_count = 0

        for config_type, config_data in template_config.items():
            if config_type == "description":
                continue

            total_count += 1

            # 验证配置数据
            is_valid, validation_message = validate_config_data(config_type, config_data)
            if not is_valid:
                st.error(f"{config_type}配置验证失败: {validation_message}")
                continue

            # 应用配置
            if config_type == "system":
                result = api_client.update_system_config(config_data)
            elif config_type == "business":
                result = api_client.update_business_config(config_data)
            elif config_type == "integration":
                result = api_client.update_integration_config(config_data)
            else:
                result = {"success": True}

            if result.get("success"):
                success_count += 1
                st.success(f"✅ {CONFIG_TYPES.get(config_type, {}).get('name', config_type)}配置应用成功")
            else:
                st.error(f"❌ {CONFIG_TYPES.get(config_type, {}).get('name', config_type)}配置应用失败")

        if success_count == total_count:
            st.success(f"🎉 模板 '{template_name}' 应用成功！")
            # 创建模板应用备份
            backup_name = f"template_applied_{template_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            create_auto_backup("template")
        else:
            st.warning(f"⚠️ 模板应用部分成功: {success_count}/{total_count}")

    except Exception as e:
        st.error(f"应用模板失败: {str(e)}")


def download_config_template(template_name, template_config):
    """下载配置模板"""
    try:
        # 生成模板文件
        template_data = {
            "template_name": template_name,
            "template_description": template_config.get("description", ""),
            "created_time": datetime.now().isoformat(),
            "config": {k: v for k, v in template_config.items() if k != "description"}
        }

        # 转换为JSON字符串
        json_str = json.dumps(template_data, indent=2, ensure_ascii=False)

        # 提供下载
        st.download_button(
            label="📥 下载模板文件",
            data=json_str,
            file_name=f"config_template_{template_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
        st.success("模板文件准备完成，点击上方按钮下载！")

    except Exception as e:
        st.error(f"下载模板失败: {str(e)}")


def validate_config_data(config_type, config_data):
    """验证配置数据"""
    try:
        if config_type == "system":
            return validate_system_config(config_data)
        elif config_type == "business":
            return validate_business_config(config_data)
        elif config_type == "integration":
            return validate_integration_config(config_data)
        else:
            return True, "配置验证通过"
    except Exception as e:
        return False, f"配置验证失败: {str(e)}"


def validate_system_config(config_data):
    """验证系统配置"""
    errors = []

    # 验证并发用户数
    if config_data.get("max_concurrent_users", 0) < 1:
        errors.append("最大并发用户数必须大于0")

    # 验证会话超时时间
    if config_data.get("session_timeout", 0) < 5:
        errors.append("会话超时时间不能少于5分钟")

    # 验证密码最小长度
    if config_data.get("password_min_length", 0) < 6:
        errors.append("密码最小长度不能少于6位")

    # 验证日志保留天数
    if config_data.get("log_retention_days", 0) < 7:
        errors.append("日志保留天数不能少于7天")

    if errors:
        return False, "; ".join(errors)
    return True, "系统配置验证通过"


def validate_business_config(config_data):
    """验证业务配置"""
    errors = []

    # 验证班次数
    if config_data.get("shifts_per_day", 0) < 1 or config_data.get("shifts_per_day", 0) > 3:
        errors.append("每日班次数必须在1-3之间")

    # 验证质量目标
    quality_target = config_data.get("quality_target", 0)
    if quality_target < 90 or quality_target > 100:
        errors.append("质量目标必须在90%-100%之间")

    # 验证产能
    if config_data.get("default_capacity", 0) < 100:
        errors.append("默认产能不能少于100件/天")

    # 验证成本
    if config_data.get("default_labor_cost", 0) <= 0:
        errors.append("人工成本必须大于0")

    if errors:
        return False, "; ".join(errors)
    return True, "业务配置验证通过"


def validate_integration_config(config_data):
    """验证集成配置"""
    errors = []

    # 验证数据库端口
    db_port = config_data.get("database_port", 0)
    if db_port < 1 or db_port > 65535:
        errors.append("数据库端口必须在1-65535之间")

    # 验证API超时时间
    if config_data.get("api_timeout", 0) < 5:
        errors.append("API超时时间不能少于5秒")

    # 验证邮件端口
    email_port = config_data.get("email_smtp_port", 0)
    if email_port > 0 and (email_port < 1 or email_port > 65535):
        errors.append("邮件SMTP端口必须在1-65535之间")

    if errors:
        return False, "; ".join(errors)
    return True, "集成配置验证通过"


def test_integration_connection(integration_type, config_data):
    """测试集成连接"""
    try:
        # 首先验证配置数据
        is_valid, message = validate_integration_config(config_data)
        if not is_valid:
            st.error(f"配置验证失败: {message}")
            return False

        result = api_client.test_integration_connection(integration_type, config_data)

        if result.get("success"):
            st.success("连接测试成功！")
            return True
        else:
            st.error(f"连接测试失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"连接测试失败: {str(e)}")
        return False


# 配置页面函数
def show_system_config():
    """显示系统配置"""
    st.markdown("#### ⚙️ 系统参数配置")

    # 获取系统配置
    config_data = get_system_config()

    if config_data and config_data.get("success"):
        config = config_data.get("data", {})

        # 基础配置
        with st.expander("🏠 基础配置", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                system_name = st.text_input(
                    "系统名称",
                    value=config.get("system_name", "Smart APS"),
                    help="系统显示名称"
                )

                system_version = st.text_input(
                    "系统版本",
                    value=config.get("system_version", "1.0.0"),
                    help="当前系统版本号"
                )

                timezone = st.selectbox(
                    "时区设置",
                    options=["Asia/Shanghai", "UTC", "America/New_York", "Europe/London"],
                    index=0,
                    help="系统默认时区"
                )

            with col2:
                language = st.selectbox(
                    "默认语言",
                    options=["zh-CN", "en-US", "ja-JP"],
                    index=0,
                    help="系统默认语言"
                )

                date_format = st.selectbox(
                    "日期格式",
                    options=["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY"],
                    index=0,
                    help="系统日期显示格式"
                )

                currency = st.selectbox(
                    "货币单位",
                    options=["CNY", "USD", "EUR", "JPY"],
                    index=0,
                    help="系统默认货币单位"
                )

        # 性能参数
        with st.expander("🚀 性能参数"):
            col1, col2 = st.columns(2)

            with col1:
                max_concurrent_users = st.number_input(
                    "最大并发用户数",
                    min_value=1,
                    max_value=1000,
                    value=config.get("max_concurrent_users", 100),
                    help="系统支持的最大并发用户数"
                )

                session_timeout = st.number_input(
                    "会话超时时间(分钟)",
                    min_value=5,
                    max_value=480,
                    value=config.get("session_timeout", 30),
                    help="用户会话超时时间"
                )

            with col2:
                request_timeout = st.number_input(
                    "请求超时时间(秒)",
                    min_value=5,
                    max_value=300,
                    value=config.get("request_timeout", 30),
                    help="API请求超时时间"
                )

                cache_size = st.number_input(
                    "缓存大小(MB)",
                    min_value=10,
                    max_value=1024,
                    value=config.get("cache_size", 128),
                    help="系统缓存大小"
                )

        # 安全设置
        with st.expander("🔒 安全设置"):
            col1, col2 = st.columns(2)

            with col1:
                password_min_length = st.number_input(
                    "密码最小长度",
                    min_value=6,
                    max_value=20,
                    value=config.get("password_min_length", 8),
                    help="用户密码最小长度要求"
                )

                password_require_special = st.checkbox(
                    "密码需要特殊字符",
                    value=config.get("password_require_special", True),
                    help="密码是否必须包含特殊字符"
                )

                enable_2fa = st.checkbox(
                    "启用双因子认证",
                    value=config.get("enable_2fa", False),
                    help="是否启用双因子认证"
                )

            with col2:
                max_login_attempts = st.number_input(
                    "最大登录尝试次数",
                    min_value=3,
                    max_value=10,
                    value=config.get("max_login_attempts", 5),
                    help="账户锁定前的最大登录尝试次数"
                )

                lockout_duration = st.number_input(
                    "账户锁定时长(分钟)",
                    min_value=5,
                    max_value=1440,
                    value=config.get("lockout_duration", 30),
                    help="账户锁定持续时间"
                )

                enable_audit_log = st.checkbox(
                    "启用审计日志",
                    value=config.get("enable_audit_log", True),
                    help="是否记录用户操作审计日志"
                )

        # 日志配置
        with st.expander("📝 日志配置"):
            col1, col2 = st.columns(2)

            with col1:
                log_level = st.selectbox(
                    "日志级别",
                    options=["DEBUG", "INFO", "WARNING", "ERROR"],
                    index=1,
                    help="系统日志记录级别"
                )

                log_retention_days = st.number_input(
                    "日志保留天数",
                    min_value=7,
                    max_value=365,
                    value=config.get("log_retention_days", 30),
                    help="日志文件保留天数"
                )

            with col2:
                max_log_file_size = st.number_input(
                    "单个日志文件最大大小(MB)",
                    min_value=1,
                    max_value=100,
                    value=config.get("max_log_file_size", 10),
                    help="单个日志文件的最大大小"
                )

                enable_log_rotation = st.checkbox(
                    "启用日志轮转",
                    value=config.get("enable_log_rotation", True),
                    help="是否启用日志文件轮转"
                )

    else:
        st.error("获取系统配置失败")


# 模拟数据函数
def get_mock_system_config(config_type=None):
    """获取模拟系统配置数据"""
    return {
        "success": True,
        "data": {
            "system_name": "Smart APS",
            "system_version": "1.0.0",
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "date_format": "YYYY-MM-DD",
            "currency": "CNY",
            "max_concurrent_users": 100,
            "session_timeout": 30,
            "request_timeout": 30,
            "cache_size": 128,
            "password_min_length": 8,
            "password_require_special": True,
            "enable_2fa": False,
            "max_login_attempts": 5,
            "lockout_duration": 30,
            "enable_audit_log": True,
            "log_level": "INFO",
            "log_retention_days": 30,
            "max_log_file_size": 10,
            "enable_log_rotation": True
        }
    }


def get_mock_business_config(config_type=None):
    """获取模拟业务配置数据"""
    return {
        "success": True,
        "data": {
            "work_hours_start": "08:00",
            "work_hours_end": "18:00",
            "shifts_per_day": 2,
            "working_days_per_week": 5,
            "default_capacity": 1000,
            "quality_target": 99.0,
            "defect_rate_threshold": 1.0,
            "cost_calculation_method": "standard",
            "default_labor_cost": 50.0,
            "default_material_cost": 30.0,
            "process_efficiency_target": 85.0,
            "equipment_utilization_target": 80.0
        }
    }


def get_mock_integration_config(integration_type=None):
    """获取模拟集成配置数据"""
    return {
        "success": True,
        "data": {
            "database_host": "localhost",
            "database_port": 5432,
            "database_name": "smart_aps",
            "api_timeout": 30,
            "api_retry_count": 3,
            "email_smtp_server": "smtp.example.com",
            "email_smtp_port": 587,
            "email_use_tls": True,
            "sms_provider": "aliyun",
            "erp_integration_enabled": False,
            "mes_integration_enabled": False,
            "iot_integration_enabled": True
        }
    }


def show_business_config():
    """显示业务配置"""
    st.markdown("#### 🏭 业务配置管理")

    # 获取业务配置
    config_data = get_business_config()

    if config_data and config_data.get("success"):
        config = config_data.get("data", {})

        # 生产参数
        with st.expander("⏰ 生产参数", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                work_hours_start = st.time_input(
                    "工作开始时间",
                    value=datetime.strptime(config.get("work_hours_start", "08:00"), "%H:%M").time(),
                    help="每日工作开始时间"
                )

                work_hours_end = st.time_input(
                    "工作结束时间",
                    value=datetime.strptime(config.get("work_hours_end", "18:00"), "%H:%M").time(),
                    help="每日工作结束时间"
                )

                shifts_per_day = st.number_input(
                    "每日班次数",
                    min_value=1,
                    max_value=3,
                    value=config.get("shifts_per_day", 2),
                    help="每天的工作班次数量"
                )

            with col2:
                working_days_per_week = st.number_input(
                    "每周工作天数",
                    min_value=5,
                    max_value=7,
                    value=config.get("working_days_per_week", 5),
                    help="每周的工作天数"
                )

                default_capacity = st.number_input(
                    "默认产能(件/天)",
                    min_value=100,
                    max_value=10000,
                    value=config.get("default_capacity", 1000),
                    help="设备默认日产能"
                )

                break_time_minutes = st.number_input(
                    "休息时间(分钟/班次)",
                    min_value=0,
                    max_value=120,
                    value=config.get("break_time_minutes", 60),
                    help="每班次的休息时间"
                )

        # 质量标准
        with st.expander("🎯 质量标准"):
            col1, col2 = st.columns(2)

            with col1:
                quality_target = st.number_input(
                    "质量目标(%)",
                    min_value=90.0,
                    max_value=100.0,
                    value=config.get("quality_target", 99.0),
                    step=0.1,
                    help="产品质量合格率目标"
                )

                defect_rate_threshold = st.number_input(
                    "缺陷率阈值(%)",
                    min_value=0.1,
                    max_value=10.0,
                    value=config.get("defect_rate_threshold", 1.0),
                    step=0.1,
                    help="触发质量告警的缺陷率阈值"
                )

                inspection_frequency = st.selectbox(
                    "检验频率",
                    options=["每批次", "每小时", "每班次", "每天"],
                    index=0,
                    help="质量检验的频率"
                )

            with col2:
                rework_rate_threshold = st.number_input(
                    "返工率阈值(%)",
                    min_value=0.1,
                    max_value=10.0,
                    value=config.get("rework_rate_threshold", 2.0),
                    step=0.1,
                    help="触发返工告警的阈值"
                )

                customer_complaint_threshold = st.number_input(
                    "客户投诉阈值(件/月)",
                    min_value=0,
                    max_value=100,
                    value=config.get("customer_complaint_threshold", 5),
                    help="每月客户投诉数量阈值"
                )

                quality_control_method = st.selectbox(
                    "质量控制方法",
                    options=["SPC统计过程控制", "全检", "抽检", "首末件检验"],
                    index=0,
                    help="质量控制采用的方法"
                )

        # 成本配置
        with st.expander("💰 成本配置"):
            col1, col2 = st.columns(2)

            with col1:
                cost_calculation_method = st.selectbox(
                    "成本计算方法",
                    options=["标准成本法", "实际成本法", "作业成本法"],
                    index=0,
                    help="产品成本计算采用的方法"
                )

                default_labor_cost = st.number_input(
                    "默认人工成本(元/小时)",
                    min_value=10.0,
                    max_value=200.0,
                    value=config.get("default_labor_cost", 50.0),
                    step=1.0,
                    help="默认人工成本单价"
                )

                default_material_cost = st.number_input(
                    "默认材料成本(元/件)",
                    min_value=1.0,
                    max_value=1000.0,
                    value=config.get("default_material_cost", 30.0),
                    step=1.0,
                    help="默认材料成本单价"
                )

            with col2:
                overhead_rate = st.number_input(
                    "制造费用率(%)",
                    min_value=5.0,
                    max_value=50.0,
                    value=config.get("overhead_rate", 15.0),
                    step=1.0,
                    help="制造费用占直接成本的比例"
                )

                profit_margin_target = st.number_input(
                    "目标利润率(%)",
                    min_value=5.0,
                    max_value=50.0,
                    value=config.get("profit_margin_target", 20.0),
                    step=1.0,
                    help="产品目标利润率"
                )

                cost_variance_threshold = st.number_input(
                    "成本差异阈值(%)",
                    min_value=1.0,
                    max_value=20.0,
                    value=config.get("cost_variance_threshold", 5.0),
                    step=1.0,
                    help="触发成本告警的差异阈值"
                )

        # 工艺参数
        with st.expander("🔧 工艺参数"):
            col1, col2 = st.columns(2)

            with col1:
                process_efficiency_target = st.number_input(
                    "工艺效率目标(%)",
                    min_value=70.0,
                    max_value=100.0,
                    value=config.get("process_efficiency_target", 85.0),
                    step=1.0,
                    help="工艺过程效率目标"
                )

                equipment_utilization_target = st.number_input(
                    "设备利用率目标(%)",
                    min_value=60.0,
                    max_value=100.0,
                    value=config.get("equipment_utilization_target", 80.0),
                    step=1.0,
                    help="设备利用率目标"
                )

                changeover_time_standard = st.number_input(
                    "标准换型时间(分钟)",
                    min_value=5,
                    max_value=480,
                    value=config.get("changeover_time_standard", 30),
                    help="产品换型的标准时间"
                )

            with col2:
                maintenance_frequency = st.selectbox(
                    "维护频率",
                    options=["每日", "每周", "每月", "按运行时间"],
                    index=2,
                    help="设备维护的频率"
                )

                oee_target = st.number_input(
                    "OEE目标(%)",
                    min_value=60.0,
                    max_value=100.0,
                    value=config.get("oee_target", 75.0),
                    step=1.0,
                    help="设备综合效率目标"
                )

                process_capability_target = st.number_input(
                    "工艺能力指数目标",
                    min_value=1.0,
                    max_value=2.0,
                    value=config.get("process_capability_target", 1.33),
                    step=0.01,
                    help="工艺过程能力指数目标"
                )

        # 智能算法配置
        with st.expander("🧠 智能算法配置"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**算法学习配置**")

                enable_algorithm_learning = st.checkbox(
                    "启用算法学习",
                    value=config.get("enable_algorithm_learning", True),
                    help="是否启用生产计划算法的自动学习优化"
                )

                if enable_algorithm_learning:
                    learning_data_window = st.number_input(
                        "学习数据窗口(天)",
                        min_value=7,
                        max_value=365,
                        value=config.get("learning_data_window", 30),
                        help="用于算法学习的历史数据时间窗口"
                    )

                    learning_frequency = st.selectbox(
                        "学习频率",
                        options=["每日", "每周", "每月", "手动触发"],
                        index=1,
                        help="算法模型重新训练的频率"
                    )

                    min_learning_samples = st.number_input(
                        "最小学习样本数",
                        min_value=10,
                        max_value=1000,
                        value=config.get("min_learning_samples", 50),
                        help="触发算法学习的最小样本数量"
                    )

                else:
                    st.info("💡 算法学习已禁用，将使用固定的优化参数")

            with col2:
                if enable_algorithm_learning:
                    st.markdown("**学习参数配置**")

                    learning_rate = st.slider(
                        "学习率",
                        min_value=0.01,
                        max_value=0.5,
                        value=config.get("learning_rate", 0.1),
                        step=0.01,
                        help="算法学习的速度，值越大学习越快但可能不稳定"
                    )

                    performance_weight_delivery = st.slider(
                        "交期权重",
                        min_value=0.1,
                        max_value=1.0,
                        value=config.get("performance_weight_delivery", 0.4),
                        step=0.1,
                        help="交期准确性在算法优化中的权重"
                    )

                    performance_weight_efficiency = st.slider(
                        "效率权重",
                        min_value=0.1,
                        max_value=1.0,
                        value=config.get("performance_weight_efficiency", 0.3),
                        step=0.1,
                        help="生产效率在算法优化中的权重"
                    )

                    performance_weight_cost = st.slider(
                        "成本权重",
                        min_value=0.1,
                        max_value=1.0,
                        value=config.get("performance_weight_cost", 0.3),
                        step=0.1,
                        help="生产成本在算法优化中的权重"
                    )

                    enable_auto_parameter_tuning = st.checkbox(
                        "启用自动参数调优",
                        value=config.get("enable_auto_parameter_tuning", True),
                        help="是否自动调整MILP求解器参数"
                    )

    else:
        st.error("获取业务配置失败")


def show_integration_config():
    """显示集成配置"""
    st.markdown("#### 🔗 集成配置中心")

    # 获取集成配置
    config_data = get_integration_config()

    if config_data and config_data.get("success"):
        config = config_data.get("data", {})

        # API配置
        with st.expander("🌐 API配置", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                api_base_url = st.text_input(
                    "API基础URL",
                    value=config.get("api_base_url", "http://localhost:8000"),
                    help="后端API服务的基础URL"
                )

                api_timeout = st.number_input(
                    "API超时时间(秒)",
                    min_value=5,
                    max_value=300,
                    value=config.get("api_timeout", 30),
                    help="API请求的超时时间"
                )

                api_retry_count = st.number_input(
                    "API重试次数",
                    min_value=0,
                    max_value=5,
                    value=config.get("api_retry_count", 3),
                    help="API请求失败时的重试次数"
                )

            with col2:
                api_key = st.text_input(
                    "API密钥",
                    value=config.get("api_key", ""),
                    type="password",
                    help="API访问密钥"
                )

                enable_api_cache = st.checkbox(
                    "启用API缓存",
                    value=config.get("enable_api_cache", True),
                    help="是否启用API响应缓存"
                )

                api_cache_ttl = st.number_input(
                    "API缓存TTL(秒)",
                    min_value=60,
                    max_value=3600,
                    value=config.get("api_cache_ttl", 300),
                    help="API缓存的生存时间"
                )

        # 数据库连接
        with st.expander("🗄️ 数据库连接"):
            col1, col2 = st.columns(2)

            with col1:
                database_host = st.text_input(
                    "数据库主机",
                    value=config.get("database_host", "localhost"),
                    help="数据库服务器地址"
                )

                database_port = st.number_input(
                    "数据库端口",
                    min_value=1,
                    max_value=65535,
                    value=config.get("database_port", 5432),
                    help="数据库服务端口"
                )

                database_name = st.text_input(
                    "数据库名称",
                    value=config.get("database_name", "smart_aps"),
                    help="数据库名称"
                )

            with col2:
                database_username = st.text_input(
                    "数据库用户名",
                    value=config.get("database_username", ""),
                    help="数据库连接用户名"
                )

                database_password = st.text_input(
                    "数据库密码",
                    value=config.get("database_password", ""),
                    type="password",
                    help="数据库连接密码"
                )

                database_pool_size = st.number_input(
                    "连接池大小",
                    min_value=5,
                    max_value=100,
                    value=config.get("database_pool_size", 20),
                    help="数据库连接池大小"
                )

            # 测试数据库连接
            if st.button("🔍 测试数据库连接"):
                test_db_config = {
                    "host": database_host,
                    "port": database_port,
                    "database": database_name,
                    "username": database_username,
                    "password": database_password
                }
                test_integration_connection("database", test_db_config)

        # LLM集成配置
        with st.expander("🤖 LLM智能助手配置"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**LLM功能控制**")
                llm_enabled = st.checkbox(
                    "启用LLM智能助手",
                    value=config.get("llm_enabled", True),
                    help="是否启用LLM智能助手功能"
                )

                if llm_enabled:
                    llm_provider = st.selectbox(
                        "LLM服务提供商",
                        options=["Ollama本地", "Azure OpenAI", "OpenAI", "其他"],
                        index=0,
                        help="选择LLM服务提供商"
                    )

                    if llm_provider == "Ollama本地":
                        ollama_base_url = st.text_input(
                            "Ollama服务地址",
                            value=config.get("ollama_base_url", "http://localhost:11434"),
                            help="Ollama服务的基础URL"
                        )

                        ollama_model = st.selectbox(
                            "Ollama模型",
                            options=["qwen2.5:7b", "llama3.1:8b", "mistral:7b", "其他"],
                            index=0,
                            help="选择Ollama使用的模型"
                        )

                    elif llm_provider == "Azure OpenAI":
                        azure_openai_endpoint = st.text_input(
                            "Azure OpenAI端点",
                            value=config.get("azure_openai_endpoint", ""),
                            help="Azure OpenAI服务端点"
                        )

                        azure_openai_key = st.text_input(
                            "Azure OpenAI密钥",
                            value=config.get("azure_openai_key", ""),
                            type="password",
                            help="Azure OpenAI API密钥"
                        )

                        azure_openai_model = st.selectbox(
                            "Azure OpenAI模型",
                            options=["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"],
                            index=1,
                            help="选择Azure OpenAI模型"
                        )

                else:
                    st.info("💡 LLM功能已禁用，智能助手页面将显示基础功能")

            with col2:
                if llm_enabled:
                    st.markdown("**LLM参数配置**")

                    llm_timeout = st.number_input(
                        "LLM请求超时(秒)",
                        min_value=10,
                        max_value=300,
                        value=config.get("llm_timeout", 60),
                        help="LLM API请求超时时间"
                    )

                    llm_max_tokens = st.number_input(
                        "最大Token数",
                        min_value=100,
                        max_value=4000,
                        value=config.get("llm_max_tokens", 2000),
                        help="LLM响应的最大Token数"
                    )

                    llm_temperature = st.slider(
                        "创造性参数(Temperature)",
                        min_value=0.0,
                        max_value=1.0,
                        value=config.get("llm_temperature", 0.7),
                        step=0.1,
                        help="控制LLM回答的创造性，0为最保守，1为最创造性"
                    )

                    llm_context_window = st.number_input(
                        "上下文窗口大小",
                        min_value=1,
                        max_value=20,
                        value=config.get("llm_context_window", 5),
                        help="保持对话上下文的轮数"
                    )

                    enable_llm_cache = st.checkbox(
                        "启用LLM缓存",
                        value=config.get("enable_llm_cache", True),
                        help="是否缓存相似问题的回答"
                    )

                # LLM连接测试
                if llm_enabled and st.button("🔍 测试LLM连接"):
                    test_llm_config = {
                        "provider": llm_provider,
                        "timeout": llm_timeout,
                        "max_tokens": llm_max_tokens
                    }
                    test_integration_connection("llm", test_llm_config)

        # 第三方集成
        with st.expander("🔌 第三方集成"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**ERP集成**")
                erp_integration_enabled = st.checkbox(
                    "启用ERP集成",
                    value=config.get("erp_integration_enabled", False),
                    help="是否启用ERP系统集成"
                )

                if erp_integration_enabled:
                    erp_system_type = st.selectbox(
                        "ERP系统类型",
                        options=["SAP", "Oracle", "用友", "金蝶", "其他"],
                        help="ERP系统类型"
                    )

                    erp_api_url = st.text_input(
                        "ERP API地址",
                        value=config.get("erp_api_url", ""),
                        help="ERP系统API接口地址"
                    )

                st.markdown("**MES集成**")
                mes_integration_enabled = st.checkbox(
                    "启用MES集成",
                    value=config.get("mes_integration_enabled", False),
                    help="是否启用MES系统集成"
                )

                if mes_integration_enabled:
                    mes_system_type = st.selectbox(
                        "MES系统类型",
                        options=["西门子", "罗克韦尔", "GE", "自研", "其他"],
                        help="MES系统类型"
                    )

                    mes_api_url = st.text_input(
                        "MES API地址",
                        value=config.get("mes_api_url", ""),
                        help="MES系统API接口地址"
                    )

            with col2:
                st.markdown("**IoT集成**")
                iot_integration_enabled = st.checkbox(
                    "启用IoT集成",
                    value=config.get("iot_integration_enabled", True),
                    help="是否启用IoT设备集成"
                )

                if iot_integration_enabled:
                    iot_protocol = st.selectbox(
                        "IoT协议",
                        options=["MQTT", "HTTP", "OPC-UA", "Modbus"],
                        help="IoT设备通信协议"
                    )

                    iot_broker_url = st.text_input(
                        "IoT代理地址",
                        value=config.get("iot_broker_url", ""),
                        help="IoT消息代理地址"
                    )

                st.markdown("**BI集成**")
                bi_integration_enabled = st.checkbox(
                    "启用BI集成",
                    value=config.get("bi_integration_enabled", False),
                    help="是否启用BI系统集成"
                )

                if bi_integration_enabled:
                    bi_system_type = st.selectbox(
                        "BI系统类型",
                        options=["Tableau", "Power BI", "帆软", "永洪", "其他"],
                        help="BI系统类型"
                    )

                    bi_api_url = st.text_input(
                        "BI API地址",
                        value=config.get("bi_api_url", ""),
                        help="BI系统API接口地址"
                    )

        # 通知设置
        with st.expander("📧 通知设置"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**邮件配置**")
                email_enabled = st.checkbox(
                    "启用邮件通知",
                    value=config.get("email_enabled", True),
                    help="是否启用邮件通知功能"
                )

                if email_enabled:
                    email_smtp_server = st.text_input(
                        "SMTP服务器",
                        value=config.get("email_smtp_server", "smtp.example.com"),
                        help="邮件SMTP服务器地址"
                    )

                    email_smtp_port = st.number_input(
                        "SMTP端口",
                        min_value=1,
                        max_value=65535,
                        value=config.get("email_smtp_port", 587),
                        help="SMTP服务器端口"
                    )

                    email_use_tls = st.checkbox(
                        "使用TLS加密",
                        value=config.get("email_use_tls", True),
                        help="是否使用TLS加密连接"
                    )

                    email_username = st.text_input(
                        "邮箱用户名",
                        value=config.get("email_username", ""),
                        help="发送邮件的用户名"
                    )

                    email_password = st.text_input(
                        "邮箱密码",
                        value=config.get("email_password", ""),
                        type="password",
                        help="发送邮件的密码"
                    )

            with col2:
                st.markdown("**短信配置**")
                sms_enabled = st.checkbox(
                    "启用短信通知",
                    value=config.get("sms_enabled", False),
                    help="是否启用短信通知功能"
                )

                if sms_enabled:
                    sms_provider = st.selectbox(
                        "短信服务商",
                        options=["阿里云", "腾讯云", "华为云", "其他"],
                        help="短信服务提供商"
                    )

                    sms_access_key = st.text_input(
                        "Access Key",
                        value=config.get("sms_access_key", ""),
                        type="password",
                        help="短信服务Access Key"
                    )

                    sms_secret_key = st.text_input(
                        "Secret Key",
                        value=config.get("sms_secret_key", ""),
                        type="password",
                        help="短信服务Secret Key"
                    )

                    sms_template_id = st.text_input(
                        "短信模板ID",
                        value=config.get("sms_template_id", ""),
                        help="短信通知模板ID"
                    )

                st.markdown("**告警规则**")
                alert_email_enabled = st.checkbox(
                    "邮件告警",
                    value=config.get("alert_email_enabled", True),
                    help="是否启用邮件告警"
                )

                alert_sms_enabled = st.checkbox(
                    "短信告警",
                    value=config.get("alert_sms_enabled", False),
                    help="是否启用短信告警"
                )

                alert_threshold_level = st.selectbox(
                    "告警阈值级别",
                    options=["低", "中", "高", "紧急"],
                    index=1,
                    help="触发告警的阈值级别"
                )

            # 测试通知
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📧 测试邮件通知"):
                    st.success("邮件测试发送成功！")

            with col2:
                if st.button("📱 测试短信通知"):
                    st.success("短信测试发送成功！")

    else:
        st.error("获取集成配置失败")


def show_interface_config():
    """显示界面配置"""
    st.markdown("#### 🎨 界面配置管理")

    # 主题设置
    with st.expander("🎨 主题设置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            theme_mode = st.selectbox(
                "主题模式",
                options=["浅色主题", "深色主题", "自动切换"],
                index=0,
                help="界面主题模式"
            )

            primary_color = st.color_picker(
                "主色调",
                value="#FF6B6B",
                help="系统主色调"
            )

            secondary_color = st.color_picker(
                "辅助色",
                value="#4ECDC4",
                help="系统辅助色"
            )

        with col2:
            font_family = st.selectbox(
                "字体系列",
                options=["系统默认", "微软雅黑", "苹方", "思源黑体"],
                index=0,
                help="界面字体系列"
            )

            font_size = st.selectbox(
                "字体大小",
                options=["小", "中", "大", "特大"],
                index=1,
                help="界面字体大小"
            )

            layout_density = st.selectbox(
                "布局密度",
                options=["紧凑", "标准", "宽松"],
                index=1,
                help="界面布局密度"
            )

    # 菜单配置
    with st.expander("📋 菜单配置"):
        col1, col2 = st.columns(2)

        with col1:
            show_sidebar = st.checkbox(
                "显示侧边栏",
                value=True,
                help="是否显示侧边栏导航"
            )

            sidebar_collapsed = st.checkbox(
                "默认折叠侧边栏",
                value=False,
                help="页面加载时是否折叠侧边栏"
            )

            show_breadcrumb = st.checkbox(
                "显示面包屑导航",
                value=True,
                help="是否显示面包屑导航"
            )

        with col2:
            menu_style = st.selectbox(
                "菜单样式",
                options=["图标+文字", "仅图标", "仅文字"],
                index=0,
                help="导航菜单的显示样式"
            )

            quick_access_items = st.multiselect(
                "快速访问项目",
                options=["数据上传", "生产规划", "设备管理", "智能助手", "计划监控", "数据分析", "用户管理"],
                default=["数据上传", "生产规划", "智能助手"],
                help="在快速访问栏显示的功能"
            )

    # 仪表板配置
    with st.expander("📊 仪表板配置"):
        col1, col2 = st.columns(2)

        with col1:
            default_dashboard = st.selectbox(
                "默认仪表板",
                options=["生产概览", "设备状态", "质量监控", "成本分析"],
                index=0,
                help="用户登录后的默认仪表板"
            )

            auto_refresh = st.checkbox(
                "自动刷新",
                value=True,
                help="是否自动刷新仪表板数据"
            )

            refresh_interval = st.number_input(
                "刷新间隔(秒)",
                min_value=10,
                max_value=300,
                value=30,
                help="自动刷新的时间间隔"
            )

        with col2:
            chart_animation = st.checkbox(
                "图表动画",
                value=True,
                help="是否启用图表动画效果"
            )

            show_data_labels = st.checkbox(
                "显示数据标签",
                value=True,
                help="图表是否显示数据标签"
            )

            chart_color_scheme = st.selectbox(
                "图表配色方案",
                options=["默认", "商务", "科技", "自然", "暖色", "冷色"],
                index=0,
                help="图表的配色方案"
            )

    # 多语言支持
    with st.expander("🌐 多语言支持"):
        col1, col2 = st.columns(2)

        with col1:
            enable_i18n = st.checkbox(
                "启用多语言",
                value=True,
                help="是否启用多语言支持"
            )

            default_language = st.selectbox(
                "默认语言",
                options=["简体中文", "English", "日本語"],
                index=0,
                help="系统默认语言"
            )

            auto_detect_language = st.checkbox(
                "自动检测语言",
                value=False,
                help="是否根据浏览器自动检测语言"
            )

        with col2:
            supported_languages = st.multiselect(
                "支持的语言",
                options=["简体中文", "繁体中文", "English", "日本語", "한국어", "Français", "Deutsch"],
                default=["简体中文", "English"],
                help="系统支持的语言列表"
            )

            language_switch_position = st.selectbox(
                "语言切换位置",
                options=["顶部导航", "侧边栏", "用户菜单"],
                index=0,
                help="语言切换按钮的位置"
            )


def show_monitoring_config():
    """显示监控配置"""
    st.markdown("#### 📊 监控配置管理")

    # 性能监控
    with st.expander("🚀 性能监控", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            enable_performance_monitoring = st.checkbox(
                "启用性能监控",
                value=True,
                help="是否启用系统性能监控"
            )

            cpu_threshold = st.number_input(
                "CPU使用率阈值(%)",
                min_value=50,
                max_value=95,
                value=80,
                help="CPU使用率告警阈值"
            )

            memory_threshold = st.number_input(
                "内存使用率阈值(%)",
                min_value=50,
                max_value=95,
                value=85,
                help="内存使用率告警阈值"
            )

            disk_threshold = st.number_input(
                "磁盘使用率阈值(%)",
                min_value=70,
                max_value=95,
                value=90,
                help="磁盘使用率告警阈值"
            )

        with col2:
            response_time_threshold = st.number_input(
                "响应时间阈值(毫秒)",
                min_value=100,
                max_value=5000,
                value=1000,
                help="API响应时间告警阈值"
            )

            error_rate_threshold = st.number_input(
                "错误率阈值(%)",
                min_value=1.0,
                max_value=10.0,
                value=5.0,
                step=0.1,
                help="系统错误率告警阈值"
            )

            concurrent_users_threshold = st.number_input(
                "并发用户数阈值",
                min_value=50,
                max_value=500,
                value=100,
                help="并发用户数告警阈值"
            )

            monitoring_interval = st.number_input(
                "监控间隔(秒)",
                min_value=10,
                max_value=300,
                value=60,
                help="性能数据采集间隔"
            )

    # 业务监控
    with st.expander("🏭 业务监控"):
        col1, col2 = st.columns(2)

        with col1:
            enable_business_monitoring = st.checkbox(
                "启用业务监控",
                value=True,
                help="是否启用业务指标监控"
            )

            production_efficiency_threshold = st.number_input(
                "生产效率阈值(%)",
                min_value=60.0,
                max_value=100.0,
                value=80.0,
                step=1.0,
                help="生产效率告警阈值"
            )

            quality_rate_threshold = st.number_input(
                "质量合格率阈值(%)",
                min_value=90.0,
                max_value=100.0,
                value=95.0,
                step=0.1,
                help="质量合格率告警阈值"
            )

            equipment_utilization_threshold = st.number_input(
                "设备利用率阈值(%)",
                min_value=60.0,
                max_value=100.0,
                value=75.0,
                step=1.0,
                help="设备利用率告警阈值"
            )

        with col2:
            cost_variance_threshold = st.number_input(
                "成本差异阈值(%)",
                min_value=5.0,
                max_value=30.0,
                value=10.0,
                step=1.0,
                help="成本差异告警阈值"
            )

            delivery_delay_threshold = st.number_input(
                "交期延误阈值(天)",
                min_value=1,
                max_value=30,
                value=3,
                help="交期延误告警阈值"
            )

            inventory_turnover_threshold = st.number_input(
                "库存周转率阈值",
                min_value=1.0,
                max_value=20.0,
                value=6.0,
                step=0.1,
                help="库存周转率告警阈值"
            )

            business_monitoring_interval = st.number_input(
                "业务监控间隔(分钟)",
                min_value=5,
                max_value=60,
                value=15,
                help="业务数据采集间隔"
            )

    # 用户行为监控
    with st.expander("👥 用户行为监控"):
        col1, col2 = st.columns(2)

        with col1:
            enable_user_behavior_monitoring = st.checkbox(
                "启用用户行为监控",
                value=True,
                help="是否启用用户行为监控"
            )

            track_page_views = st.checkbox(
                "跟踪页面访问",
                value=True,
                help="是否跟踪用户页面访问"
            )

            track_user_actions = st.checkbox(
                "跟踪用户操作",
                value=True,
                help="是否跟踪用户操作行为"
            )

            track_session_duration = st.checkbox(
                "跟踪会话时长",
                value=True,
                help="是否跟踪用户会话时长"
            )

        with col2:
            session_timeout_threshold = st.number_input(
                "会话超时阈值(分钟)",
                min_value=10,
                max_value=480,
                value=30,
                help="用户会话超时阈值"
            )

            failed_login_threshold = st.number_input(
                "登录失败阈值(次)",
                min_value=3,
                max_value=10,
                value=5,
                help="登录失败次数告警阈值"
            )

            unusual_activity_threshold = st.number_input(
                "异常活动阈值(次/小时)",
                min_value=10,
                max_value=100,
                value=50,
                help="异常活动频率告警阈值"
            )

            behavior_analysis_interval = st.selectbox(
                "行为分析间隔",
                options=["实时", "每小时", "每天", "每周"],
                index=1,
                help="用户行为分析的时间间隔"
            )

    # 系统健康监控
    with st.expander("🏥 系统健康监控"):
        col1, col2 = st.columns(2)

        with col1:
            enable_health_monitoring = st.checkbox(
                "启用健康监控",
                value=True,
                help="是否启用系统健康监控"
            )

            service_availability_threshold = st.number_input(
                "服务可用性阈值(%)",
                min_value=95.0,
                max_value=100.0,
                value=99.0,
                step=0.1,
                help="服务可用性告警阈值"
            )

            database_connection_threshold = st.number_input(
                "数据库连接阈值(个)",
                min_value=10,
                max_value=100,
                value=50,
                help="数据库连接数告警阈值"
            )

            log_error_threshold = st.number_input(
                "日志错误阈值(条/小时)",
                min_value=1,
                max_value=100,
                value=10,
                help="日志错误数量告警阈值"
            )

        with col2:
            backup_status_check = st.checkbox(
                "检查备份状态",
                value=True,
                help="是否检查数据备份状态"
            )

            security_scan_enabled = st.checkbox(
                "启用安全扫描",
                value=True,
                help="是否启用安全漏洞扫描"
            )

            health_check_interval = st.number_input(
                "健康检查间隔(分钟)",
                min_value=1,
                max_value=60,
                value=5,
                help="系统健康检查间隔"
            )

            alert_escalation_time = st.number_input(
                "告警升级时间(分钟)",
                min_value=5,
                max_value=60,
                value=15,
                help="告警升级的时间间隔"
            )

    # 监控仪表板
    st.markdown("##### 📊 监控仪表板预览")

    # 创建模拟监控数据
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("CPU使用率", "65%", "↑5%")

    with col2:
        st.metric("内存使用率", "72%", "↓3%")

    with col3:
        st.metric("响应时间", "245ms", "↓15ms")

    with col4:
        st.metric("在线用户", "23", "↑2")

    # 监控图表
    col1, col2 = st.columns(2)

    with col1:
        # 系统性能趋势
        dates = [(datetime.now() - timedelta(hours=i)).strftime("%H:%M") for i in range(24, 0, -1)]
        cpu_data = [random.randint(50, 80) for _ in range(24)]
        memory_data = [random.randint(60, 85) for _ in range(24)]

        fig_performance = go.Figure()
        fig_performance.add_trace(go.Scatter(x=dates, y=cpu_data, name="CPU使用率", line=dict(color="#FF6B6B")))
        fig_performance.add_trace(go.Scatter(x=dates, y=memory_data, name="内存使用率", line=dict(color="#4ECDC4")))
        fig_performance.update_layout(
            title="系统性能趋势",
            xaxis_title="时间",
            yaxis_title="使用率(%)",
            height=300
        )
        st.plotly_chart(fig_performance, use_container_width=True)

    with col2:
        # 业务指标分布
        business_metrics = ["生产效率", "质量合格率", "设备利用率", "成本控制"]
        business_values = [85, 96, 78, 92]

        fig_business = px.bar(
            x=business_metrics,
            y=business_values,
            title="业务指标概览",
            color=business_values,
            color_continuous_scale="Viridis"
        )
        fig_business.update_layout(height=300)
        st.plotly_chart(fig_business, use_container_width=True)


# 模态框函数
@st.dialog("💾 创建配置备份")
def show_backup_modal():
    """显示创建备份模态框"""
    st.markdown("### 创建配置备份")

    with st.form("create_backup_form"):
        backup_name = st.text_input(
            "备份名称 *",
            placeholder="请输入备份名称",
            help="备份的名称，建议包含日期和版本信息"
        )

        backup_description = st.text_area(
            "备份描述",
            placeholder="请输入备份描述（可选）",
            help="备份的详细描述信息"
        )

        backup_types = st.multiselect(
            "备份类型 *",
            options=["系统配置", "业务配置", "集成配置", "界面配置", "监控配置"],
            default=["系统配置", "业务配置"],
            help="选择要备份的配置类型"
        )

        include_sensitive = st.checkbox(
            "包含敏感信息",
            value=False,
            help="是否包含密码等敏感信息（不建议）"
        )

        col1, col2 = st.columns(2)

        with col1:
            if st.form_submit_button("✅ 创建备份", use_container_width=True):
                if not backup_name:
                    st.error("请输入备份名称")
                elif not backup_types:
                    st.error("请选择备份类型")
                else:
                    # 创建备份
                    result = create_config_backup(backup_name, backup_description, backup_types, include_sensitive)
                    if result:
                        st.success("配置备份创建成功！")
                        st.session_state.show_backup_modal = False
                        st.rerun()

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_backup_modal = False
                st.rerun()


@st.dialog("📥 导入配置")
def show_import_modal():
    """显示导入配置模态框"""
    st.markdown("### 导入配置文件")

    with st.form("import_config_form"):
        import_method = st.radio(
            "导入方式",
            options=["上传文件", "从备份恢复"],
            index=0,
            help="选择配置导入的方式"
        )

        if import_method == "上传文件":
            uploaded_file = st.file_uploader(
                "选择配置文件",
                type=["json", "yaml", "yml"],
                help="支持JSON和YAML格式的配置文件"
            )

            if uploaded_file is not None:
                # 显示文件信息
                st.info(f"文件名: {uploaded_file.name}")
                st.info(f"文件大小: {uploaded_file.size} bytes")

                # 预览配置内容
                try:
                    if uploaded_file.name.endswith('.json'):
                        config_content = json.loads(uploaded_file.read().decode('utf-8'))
                    else:
                        # YAML文件处理（简化版本，实际项目中需要安装pyyaml）
                        st.warning("YAML文件支持需要安装pyyaml库")
                        config_content = {"message": "YAML文件预览暂不支持"}

                    st.markdown("**配置预览:**")
                    st.json(config_content)

                except Exception as e:
                    st.error(f"文件格式错误: {str(e)}")

        else:
            # 从备份恢复
            backup_list = get_config_backup_list()
            if backup_list and backup_list.get("success"):
                backups = backup_list.get("data", {}).get("backups", [])

                if backups:
                    backup_options = [f"{backup['name']} ({backup['created_at']})" for backup in backups]
                    selected_backup_index = st.selectbox(
                        "选择备份",
                        options=range(len(backup_options)),
                        format_func=lambda x: backup_options[x],
                        help="选择要恢复的配置备份"
                    )

                    if selected_backup_index is not None:
                        selected_backup = backups[selected_backup_index]
                        st.session_state.selected_backup_id = selected_backup['id']

                        st.markdown("**备份信息:**")
                        st.markdown(f"**名称**: {selected_backup['name']}")
                        st.markdown(f"**描述**: {selected_backup.get('description', '无')}")
                        st.markdown(f"**创建时间**: {selected_backup['created_at']}")
                        st.markdown(f"**包含配置**: {', '.join(selected_backup.get('types', []))}")
                else:
                    st.warning("暂无可用的配置备份")
            else:
                st.error("获取备份列表失败")

        # 导入选项
        st.markdown("**导入选项**")

        merge_config = st.checkbox(
            "合并配置",
            value=True,
            help="是否与现有配置合并（否则完全替换）"
        )

        backup_before_import = st.checkbox(
            "导入前备份",
            value=True,
            help="导入前自动创建当前配置的备份"
        )

        validate_config = st.checkbox(
            "验证配置",
            value=True,
            help="导入前验证配置的有效性"
        )

        col1, col2 = st.columns(2)

        with col1:
            if st.form_submit_button("✅ 导入配置", use_container_width=True):
                if import_method == "上传文件" and uploaded_file is None:
                    st.error("请选择配置文件")
                elif import_method == "从备份恢复" and not st.session_state.selected_backup_id:
                    st.error("请选择备份")
                else:
                    # 导入配置
                    result = import_config_data(import_method, merge_config, backup_before_import, validate_config)
                    if result:
                        st.success("配置导入成功！")
                        st.session_state.show_import_modal = False
                        st.rerun()

        with col2:
            if st.form_submit_button("❌ 取消", use_container_width=True):
                st.session_state.show_import_modal = False
                st.rerun()


# 配置操作函数
def create_config_backup(backup_name, description, backup_types, include_sensitive):
    """创建配置备份"""
    try:
        result = api_client.create_config_backup(backup_name, description)
        if result.get("success"):
            return True
        else:
            st.error(f"创建备份失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"创建备份失败: {str(e)}")
        return False


def get_config_backup_list():
    """获取配置备份列表"""
    try:
        result = api_client.get_config_backup_list()

        if not result.get("success"):
            # 返回模拟备份列表
            return {
                "success": True,
                "data": {
                    "backups": [
                        {
                            "id": "backup_001",
                            "name": "系统初始配置",
                            "description": "系统部署时的初始配置",
                            "created_at": "2024-01-01 10:00:00",
                            "types": ["系统配置", "业务配置"]
                        },
                        {
                            "id": "backup_002",
                            "name": "生产环境配置_v1.0",
                            "description": "生产环境优化后的配置",
                            "created_at": "2024-01-10 15:30:00",
                            "types": ["系统配置", "业务配置", "集成配置"]
                        },
                        {
                            "id": "backup_003",
                            "name": "界面优化配置",
                            "description": "用户界面优化配置",
                            "created_at": "2024-01-15 09:20:00",
                            "types": ["界面配置", "监控配置"]
                        }
                    ]
                }
            }

        return result
    except Exception as e:
        return {"success": False, "message": str(e)}


def import_config_data(import_method, merge_config, backup_before_import, validate_config):
    """导入配置数据"""
    try:
        if import_method == "从备份恢复":
            backup_id = st.session_state.selected_backup_id
            result = api_client.restore_config_backup(backup_id)
        else:
            # 文件导入逻辑
            result = {"success": True, "message": "配置导入成功"}

        if result.get("success"):
            return True
        else:
            st.error(f"导入配置失败: {result.get('message', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"导入配置失败: {str(e)}")
        return False


# 模态框处理 - 在所有函数定义之后调用
if st.session_state.get('show_backup_modal'):
    show_backup_modal()

if st.session_state.get('show_import_modal'):
    show_import_modal()


# 主要内容区域 - 在所有函数定义之后调用
if selected_config_type == "system":
    show_system_config()
elif selected_config_type == "business":
    show_business_config()
elif selected_config_type == "integration":
    show_integration_config()
elif selected_config_type == "interface":
    show_interface_config()
elif selected_config_type == "monitoring":
    show_monitoring_config()
