import os
import sys

print("Quick AI Enhancement Check")
print("=" * 30)

# 检查文件是否存在
files_to_check = [
    "frontend/services/ai_enhancement_service.py",
    "frontend/pages/12_AI能力增强.py",
    "frontend/config/ai_enhancement_config.py",
    "backend/routers/ai_enhancement.py"
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path}")
    else:
        print(f"❌ {file_path}")

print("\nChecking main.py integration...")
if os.path.exists("frontend/main.py"):
    with open("frontend/main.py", "r", encoding="utf-8") as f:
        content = f.read()
        if "AI能力增强" in content:
            print("✅ AI enhancement navigation found in main.py")
        else:
            print("❌ AI enhancement navigation NOT found in main.py")

print("\nChecking API integration...")
if os.path.exists("backend/app/api/v1/api.py"):
    with open("backend/app/api/v1/api.py", "r", encoding="utf-8") as f:
        content = f.read()
        if "ai_enhancement" in content:
            print("✅ AI enhancement router found in API")
        else:
            print("❌ AI enhancement router NOT found in API")

print("\nBasic import test...")
try:
    sys.path.append("frontend")
    from services.ai_enhancement_service import AIEnhancementService
    print("✅ AI service import successful")
except Exception as e:
    print(f"❌ AI service import failed: {e}")

print("\nCheck complete!")
