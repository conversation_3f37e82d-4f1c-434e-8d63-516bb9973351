# 🤖 Smart APS AI能力增强使用指南

## 📋 概述

Smart APS AI能力增强模块集成了预测分析、异常检测、智能优化等高级AI功能，为生产管理提供智能化决策支持。

## 🚀 功能特性

### 1. 📈 预测分析
- **需求预测**: 基于历史数据预测未来需求趋势
- **设备故障预测**: 预测设备故障概率和时间
- **质量预测**: 预测产品质量指标
- **产能预测**: 预测生产能力和效率

### 2. 🔍 异常检测
- **统计异常检测**: 基于统计方法检测数据异常
- **孤立森林**: 使用机器学习检测复杂异常模式
- **LSTM自编码器**: 深度学习异常检测
- **集成检测**: 多种方法融合提高检测准确率

### 3. ⚡ 智能优化
- **生产排程优化**: 优化生产计划以最小化完工时间
- **资源分配优化**: 优化资源配置以最小化成本
- **库存优化**: 优化库存水平平衡成本和服务水平
- **能耗优化**: 优化能源使用降低运营成本

## 🛠️ 安装和配置

### 环境要求
```bash
Python >= 3.8
pandas >= 1.3.0
numpy >= 1.21.0
scikit-learn >= 1.0.0
plotly >= 5.0.0
streamlit >= 1.10.0
fastapi >= 0.70.0
```

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置文件
AI增强功能的配置位于 `frontend/config/ai_enhancement_config.py`，包含：
- 模型参数配置
- 性能阈值设置
- 数据处理配置
- 安全和集成设置

## 📖 使用指南

### 1. 前端界面使用

#### 访问AI能力增强页面
1. 启动Smart APS系统
2. 在侧边栏选择"AI能力增强"
3. 进入AI功能主界面

#### 预测分析操作
```python
# 在预测分析标签页中：
1. 选择预测类型（需求预测/设备故障预测等）
2. 设置预测周期和置信水平
3. 点击"开始预测"按钮
4. 查看预测结果和可视化图表
```

#### 异常检测操作
```python
# 在异常检测标签页中：
1. 选择检测方法（统计/孤立森林/集成等）
2. 调整检测灵敏度
3. 选择数据源
4. 点击"开始检测"按钮
5. 查看异常检测结果和详情
```

#### 智能优化操作
```python
# 在智能优化标签页中：
1. 选择优化类型（生产排程/资源分配等）
2. 设置优化目标和约束级别
3. 点击"开始优化"按钮
4. 查看优化结果和改进效果
```

### 2. API接口使用

#### 初始化AI服务
```python
import requests

# 初始化AI服务
response = requests.post("http://localhost:8000/ai-enhancement/initialize")
print(response.json())
```

#### 需求预测API
```python
# 需求预测
prediction_data = {
    "prediction_type": "需求预测",
    "input_data": {
        "base_demand": 100,
        "historical_data": "30天历史数据"
    },
    "forecast_horizon": 30,
    "confidence_level": 0.95
}

response = requests.post(
    "http://localhost:8000/ai-enhancement/predict",
    json=prediction_data
)
result = response.json()
print(f"预测结果: {result['predictions']}")
```

#### 异常检测API
```python
# 异常检测
anomaly_data = {
    "detection_type": "ensemble",
    "data": [
        {"timestamp": "2024-01-15T10:00:00", "value": 100, "equipment": "L01"},
        {"timestamp": "2024-01-15T11:00:00", "value": 150, "equipment": "L01"},  # 异常值
        {"timestamp": "2024-01-15T12:00:00", "value": 95, "equipment": "L01"}
    ],
    "sensitivity": 0.7
}

response = requests.post(
    "http://localhost:8000/ai-enhancement/detect-anomalies",
    json=anomaly_data
)
result = response.json()
print(f"检测到 {len(result['anomalies'])} 个异常")
```

#### 智能优化API
```python
# 生产排程优化
optimization_data = {
    "optimization_type": "生产排程优化",
    "input_data": {
        "orders": [
            {"order_id": "O001", "processing_time": 120, "priority": 1},
            {"order_id": "O002", "processing_time": 90, "priority": 2}
        ],
        "equipment": {
            "L01": {"status": "available", "efficiency": 0.9},
            "L02": {"status": "available", "efficiency": 0.85}
        }
    },
    "constraints": {
        "max_makespan": 480,  # 8小时
        "equipment_capacity": True
    }
}

response = requests.post(
    "http://localhost:8000/ai-enhancement/optimize",
    json=optimization_data
)
result = response.json()
print(f"优化改进: {result['improvement_percentage']:.1f}%")
```

### 3. 编程接口使用

#### 直接使用AI服务
```python
from frontend.services.ai_enhancement_service import ai_enhancement_service
import asyncio

async def main():
    # 初始化AI服务
    init_result = await ai_enhancement_service.initialize_ai_services()
    print(f"初始化结果: {init_result}")
    
    # 需求预测
    input_data = {"base_demand": 100}
    prediction = await ai_enhancement_service.predictive_analytics.predict_demand(
        input_data, forecast_horizon=7
    )
    print(f"7天需求预测: {prediction.predictions}")
    
    # 异常检测
    import pandas as pd
    data = pd.DataFrame([
        {"value": 100, "timestamp": "2024-01-15T10:00:00"},
        {"value": 150, "timestamp": "2024-01-15T11:00:00"},
        {"value": 95, "timestamp": "2024-01-15T12:00:00"}
    ])
    
    anomalies = await ai_enhancement_service.anomaly_detection.detect_anomalies(data)
    print(f"检测到异常: {len(anomalies.anomalies)}")
    
    # 智能优化
    from frontend.services.ai_enhancement_service import OptimizationType
    
    optimization_data = {
        "orders": [{"order_id": "O001", "processing_time": 120}],
        "equipment": {"L01": {"status": "available", "efficiency": 0.9}}
    }
    
    optimization = await ai_enhancement_service.intelligent_optimization.optimize(
        OptimizationType.PRODUCTION_SCHEDULE, optimization_data
    )
    print(f"优化改进: {optimization.improvement_percentage:.1f}%")

# 运行示例
asyncio.run(main())
```

## 🔧 配置和定制

### 模型参数调整
```python
# 在 ai_enhancement_config.py 中调整模型参数
PREDICTION_CONFIGS["demand_forecast"].model_config.parameters = {
    "n_estimators": 150,  # 增加树的数量
    "max_depth": 12,      # 增加树的深度
    "random_state": 42
}
```

### 性能阈值设置
```python
# 调整性能阈值
PERFORMANCE_BENCHMARKS["prediction_accuracy"]["demand_forecast"] = {
    "target": 0.95,      # 目标准确率
    "acceptable": 0.90,  # 可接受准确率
    "poor": 0.85         # 较差准确率
}
```

### 异常检测灵敏度
```python
# 调整异常检测灵敏度
ANOMALY_DETECTION_CONFIGS["production_monitoring"].sensitivity = 0.8  # 提高灵敏度
ANOMALY_DETECTION_CONFIGS["production_monitoring"].threshold_config = {
    "statistical_threshold": 2.5,  # 降低统计阈值
    "isolation_threshold": -0.05,  # 调整孤立森林阈值
    "ensemble_threshold": 0.5      # 调整集成阈值
}
```

## 📊 性能监控

### 查看服务状态
```python
# 获取AI服务状态
response = requests.get("http://localhost:8000/ai-enhancement/status")
status = response.json()

for service in status:
    print(f"服务: {service['service_name']}")
    print(f"状态: {service['status']}")
    print(f"性能指标: {service['performance_metrics']}")
```

### 模型性能监控
```python
# 获取模型性能
response = requests.get("http://localhost:8000/ai-enhancement/models/performance")
performance = response.json()

print(f"需求预测准确率: {performance['performance_data']['demand_forecast']['accuracy']}")
print(f"异常检测精确率: {performance['performance_data']['anomaly_detection']['precision']}")
```

### 健康检查
```python
# 系统健康检查
response = requests.get("http://localhost:8000/ai-enhancement/health")
health = response.json()

print(f"系统状态: {health['status']}")
print(f"CPU使用率: {health['system_metrics']['cpu_usage']:.1f}%")
print(f"内存使用率: {health['system_metrics']['memory_usage']:.1f}%")
```

## 🚨 故障排除

### 常见问题

#### 1. 预测准确率低
```python
# 解决方案：
1. 检查输入数据质量
2. 增加训练数据量
3. 调整模型参数
4. 重新训练模型

# 重新训练模型
response = requests.post(
    "http://localhost:8000/ai-enhancement/retrain-models",
    json={"model_types": ["demand_forecast"]}
)
```

#### 2. 异常检测误报率高
```python
# 解决方案：
1. 降低检测灵敏度
2. 调整阈值参数
3. 使用集成检测方法
4. 增加训练数据

# 调整检测参数
anomaly_data["sensitivity"] = 0.5  # 降低灵敏度
```

#### 3. 优化效果不明显
```python
# 解决方案：
1. 检查约束条件设置
2. 调整优化目标
3. 增加优化迭代次数
4. 使用不同的优化算法

# 调整优化参数
optimization_data["constraints"]["relaxed"] = True
```

### 日志查看
```bash
# 查看AI服务日志
tail -f logs/ai_enhancement.log

# 查看错误日志
grep "ERROR" logs/ai_enhancement.log
```

## 📈 最佳实践

### 1. 数据准备
- 确保数据质量和完整性
- 定期清理和预处理数据
- 建立数据验证机制

### 2. 模型管理
- 定期评估模型性能
- 实施A/B测试验证改进
- 建立模型版本控制

### 3. 系统监控
- 设置性能告警阈值
- 监控系统资源使用
- 定期备份模型和配置

### 4. 持续优化
- 收集用户反馈
- 分析业务效果
- 持续改进算法和参数

## 🔗 相关资源

- [Smart APS用户手册](./USER_MANUAL.md)
- [API文档](./API_DOCUMENTATION.md)
- [系统架构文档](./ARCHITECTURE.md)
- [故障排除指南](./TROUBLESHOOTING.md)

## 📞 技术支持

如有问题或需要技术支持，请联系：
- 邮箱: <EMAIL>
- 电话: 400-xxx-xxxx
- 在线文档: https://docs.smart-aps.com
