"""
算法驱动的生产计划生成演示页面
展示如何使用集成数据和算法生成生产计划
"""

import streamlit as st
import json
import time
from datetime import datetime, timedelta

from utils.auth import check_authentication, require_permission
from services.algorithm_planning_service import algorithm_planning_service
from services.data_integration_service import data_integration_service

# 页面配置
st.set_page_config(
    page_title="算法计划生成 - Smart APS",
    page_icon="🧮",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 页面标题
st.title("🧮 算法驱动的生产计划生成")
st.markdown("### 基于集成数据的智能优化算法")

# 侧边栏 - 算法配置
with st.sidebar:
    st.markdown("### ⚙️ 算法配置")
    
    # 优化目标
    optimization_objective = st.selectbox(
        "优化目标",
        ["最小化完工时间", "最大化设备利用率", "最小化成本", "平衡多目标"],
        help="选择生产计划的主要优化目标"
    )
    
    # 算法类型
    algorithm_type = st.selectbox(
        "算法类型",
        ["遗传算法", "模拟退火", "贪心算法"],
        help="选择优化算法"
    )
    
    # 时间范围
    time_horizon = st.selectbox(
        "计划周期",
        [3, 7, 14, 30],
        format_func=lambda x: f"{x}天",
        index=1
    )
    
    # 数据源选择
    st.markdown("### 📊 数据源")
    use_equipment_data = st.checkbox("设备数据", value=True)
    use_pci_data = st.checkbox("PCI数据", value=True)
    use_user_inputs = st.checkbox("用户输入", value=True)
    use_historical_data = st.checkbox("历史数据", value=True)
    
    st.markdown("---")
    
    # 生成按钮
    if st.button("🚀 生成计划", type="primary", use_container_width=True):
        st.session_state.generate_plan = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["📊 数据概览", "🧮 算法执行", "📋 计划结果", "📈 性能分析"])

with tab1:
    st.markdown("#### 📊 集成数据概览")
    
    # 获取数据上下文
    try:
        data_context = data_integration_service.get_comprehensive_data_context()
        algorithm_data = data_integration_service.get_algorithm_input_data()
        
        # 数据摘要
        summary = data_context.get("summary", {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            equipment_total = summary.get("equipment_status", {}).get("total", 0)
            equipment_available = summary.get("equipment_status", {}).get("available", 0)
            st.metric("设备数据", f"{equipment_available}/{equipment_total}", 
                     delta=f"可用: {equipment_available}")
        
        with col2:
            pci_total = summary.get("pci_status", {}).get("total_fs_items", 0)
            pci_old = summary.get("pci_status", {}).get("old_items", 0)
            st.metric("PCI物料", f"{pci_total}个", 
                     delta=f"超龄: {pci_old}")
        
        with col3:
            files_count = summary.get("data_files", {}).get("recent_files", 0)
            st.metric("数据文件", f"{files_count}个")
        
        with col4:
            constraints_count = len(data_context.get("constraints", []))
            st.metric("约束条件", f"{constraints_count}个")
        
        # 详细数据展示
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("##### ⚙️ 设备状态")
            equipment_data = algorithm_data.get("equipment", {})
            capacity_summary = equipment_data.get("capacity_summary", {})
            
            if capacity_summary:
                production_lines = capacity_summary.get("production_lines", {})
                st.write(f"**总产能**: {production_lines.get('total_capacity', 0)} 件/小时")
                st.write(f"**可用产能**: {production_lines.get('available_capacity', 0):.1f} 件/小时")
                st.write(f"**利用率**: {production_lines.get('utilization_rate', 0):.1f}%")
        
        with col2:
            st.markdown("##### 📦 物料状态")
            materials_data = algorithm_data.get("materials", {})
            
            st.write(f"**FS物料总数**: {materials_data.get('fs_inventory', []) and len(materials_data['fs_inventory']) or 0}")
            priority_count = len([p for p in materials_data.get('consumption_priorities', []) if p.get('priority', 0) > 80])
            st.write(f"**高优先级物料**: {priority_count}个")
        
        # 约束条件
        constraints = data_context.get("constraints", [])
        if constraints:
            st.markdown("##### ⚠️ 当前约束条件")
            for constraint in constraints:
                st.warning(f"• {constraint.get('description', '未知约束')}")
        
        # 建议
        recommendations = data_context.get("recommendations", [])
        if recommendations:
            st.markdown("##### 💡 系统建议")
            for rec in recommendations:
                st.info(f"• {rec}")
                
    except Exception as e:
        st.error(f"获取数据失败: {str(e)}")

with tab2:
    st.markdown("#### 🧮 算法执行过程")
    
    if st.session_state.get('generate_plan'):
        # 显示算法执行过程
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # 模拟算法执行步骤
        steps = [
            ("数据预处理", 20),
            ("约束条件设置", 40),
            ("算法初始化", 60),
            ("优化求解", 80),
            ("结果生成", 100)
        ]
        
        for step_name, progress in steps:
            status_text.text(f"正在执行: {step_name}...")
            progress_bar.progress(progress)
            time.sleep(1)
        
        status_text.text("算法执行完成！")
        
        # 执行算法
        try:
            # 映射优化目标
            objective_mapping = {
                "最小化完工时间": "minimize_makespan",
                "最大化设备利用率": "maximize_efficiency",
                "最小化成本": "minimize_cost",
                "平衡多目标": "balanced"
            }
            
            # 映射算法类型
            algorithm_mapping = {
                "遗传算法": "genetic",
                "模拟退火": "simulated_annealing",
                "贪心算法": "greedy"
            }
            
            result = algorithm_planning_service.generate_production_plan(
                optimization_objective=objective_mapping.get(optimization_objective, "balanced"),
                time_horizon_days=time_horizon,
                algorithm_type=algorithm_mapping.get(algorithm_type, "genetic"),
                constraints={}
            )
            
            st.session_state.algorithm_result = result
            st.session_state.generate_plan = False
            
            if result.get("success"):
                st.success("🎉 算法执行成功！")
                st.balloons()
            else:
                st.error(f"❌ 算法执行失败: {result.get('message')}")
                
        except Exception as e:
            st.error(f"❌ 算法执行异常: {str(e)}")
    
    else:
        st.info("点击侧边栏的 '🚀 生成计划' 按钮开始算法执行")
        
        # 显示算法信息
        st.markdown("##### 📋 算法信息")
        
        algorithm_info = {
            "遗传算法": {
                "描述": "基于自然选择和遗传机制的全局优化算法",
                "优势": "全局搜索能力强，适合复杂约束问题",
                "参数": "种群大小: 100, 迭代次数: 500, 交叉率: 0.8, 变异率: 0.1"
            },
            "模拟退火": {
                "描述": "模拟金属退火过程的概率性优化算法",
                "优势": "能够跳出局部最优，收敛性好",
                "参数": "初始温度: 1000, 降温率: 0.95, 最低温度: 0.01"
            },
            "贪心算法": {
                "描述": "每步选择当前最优解的启发式算法",
                "优势": "计算速度快，适合实时调度",
                "参数": "启发式规则: 最早交期优先, 最短处理时间优先"
            }
        }
        
        if algorithm_type in algorithm_info:
            info = algorithm_info[algorithm_type]
            st.write(f"**算法描述**: {info['描述']}")
            st.write(f"**主要优势**: {info['优势']}")
            st.write(f"**关键参数**: {info['参数']}")

with tab3:
    st.markdown("#### 📋 生成的计划结果")
    
    if st.session_state.get('algorithm_result'):
        result = st.session_state.algorithm_result
        
        if result.get("success"):
            plan_data = result.get("plan_data", {})
            evaluation = result.get("evaluation", {})
            
            # 基本信息
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("计划ID", result.get("plan_id", "")[:8] + "...")
            with col2:
                st.metric("算法类型", result.get("algorithm_type", ""))
            with col3:
                st.metric("优化目标", result.get("optimization_objective", ""))
            with col4:
                st.metric("计划周期", f"{result.get('time_horizon', 0)}天")
            
            # 性能指标
            st.markdown("##### 📊 性能指标")
            metrics = evaluation.get("metrics", {})
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("效率评分", f"{metrics.get('efficiency_score', 0):.1f}", 
                         delta=f"{metrics.get('efficiency_score', 0) - 80:.1f}")
            with col2:
                st.metric("成本评分", f"{metrics.get('cost_score', 0):.1f}",
                         delta=f"{metrics.get('cost_score', 0) - 80:.1f}")
            with col3:
                st.metric("交期评分", f"{metrics.get('delivery_score', 0):.1f}",
                         delta=f"{metrics.get('delivery_score', 0) - 85:.1f}")
            with col4:
                st.metric("质量评分", f"{metrics.get('quality_score', 0):.1f}",
                         delta=f"{metrics.get('quality_score', 0) - 85:.1f}")
            
            # 详细计划
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("##### ⚙️ 设备分配")
                equipment_allocation = plan_data.get("equipment_allocation", {})
                for equipment_id, allocation in equipment_allocation.items():
                    with st.expander(f"设备 {equipment_id}"):
                        st.write(f"**分配订单**: {', '.join(allocation.get('orders', []))}")
                        st.write(f"**利用率**: {allocation.get('utilization', 0):.1f}%")
                        st.write(f"**工作时长**: {allocation.get('total_hours', 0):.1f} 小时")
            
            with col2:
                st.markdown("##### 📦 物料消耗")
                material_consumption = plan_data.get("material_consumption", {})
                
                if material_consumption.get("priority_materials"):
                    st.write(f"**优先物料**: {', '.join(material_consumption['priority_materials'])}")
                    st.write(f"**消耗策略**: {material_consumption.get('consumption_order', 'FIFO')}")
                    
                    # 预计消耗
                    estimated_consumption = material_consumption.get("estimated_consumption", {})
                    if estimated_consumption:
                        st.markdown("**预计消耗量**:")
                        for material_id, consumption in estimated_consumption.items():
                            st.write(f"- {material_id}: {consumption.get('quantity', 0)} 单位 ({consumption.get('consumption_date', '')})")
            
            # 时间安排
            st.markdown("##### 📅 时间安排")
            timeline = plan_data.get("timeline", {})
            performance = plan_data.get("performance_metrics", {})
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.write(f"**开始日期**: {timeline.get('start_date', '')}")
                st.write(f"**结束日期**: {timeline.get('end_date', '')}")
            with col2:
                st.write(f"**总订单数**: {performance.get('total_orders', 0)}")
                st.write(f"**平均利用率**: {performance.get('equipment_utilization', 0):.1f}%")
            with col3:
                st.write(f"**按时交付率**: {performance.get('on_time_delivery_rate', 0):.1f}%")
            
            # 优化建议
            recommendations = evaluation.get("recommendations", [])
            if recommendations:
                st.markdown("##### 💡 优化建议")
                for i, rec in enumerate(recommendations):
                    st.info(f"{i+1}. {rec}")
            
            # 操作按钮
            st.markdown("---")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                if st.button("✅ 应用计划", use_container_width=True):
                    with st.spinner("正在应用计划..."):
                        time.sleep(2)
                    st.success("计划已应用到系统！")
            
            with col2:
                if st.button("📊 更新图表", use_container_width=True):
                    st.success("图表已更新！")
                    st.info("请查看生产规划页面的甘特图")
            
            with col3:
                if st.button("🔄 重新生成", use_container_width=True):
                    st.session_state.generate_plan = True
                    st.rerun()
            
            with col4:
                # 导出按钮
                export_data = {
                    "plan_info": result,
                    "export_time": datetime.now().isoformat(),
                    "export_format": "JSON"
                }
                
                st.download_button(
                    label="📥 导出计划",
                    data=json.dumps(export_data, indent=2, ensure_ascii=False),
                    file_name=f"algorithm_plan_{result.get('plan_id', 'unknown')}.json",
                    mime="application/json",
                    use_container_width=True
                )
        
        else:
            st.error(f"❌ 计划生成失败: {result.get('message', '未知错误')}")
    
    else:
        st.info("请先在 '🧮 算法执行' 标签页中生成计划")

with tab4:
    st.markdown("#### 📈 性能分析")
    
    if st.session_state.get('algorithm_result'):
        result = st.session_state.algorithm_result
        
        if result.get("success"):
            evaluation = result.get("evaluation", {})
            
            # 综合评分
            overall_score = evaluation.get("overall_score", 0)
            st.markdown(f"##### 🎯 综合评分: {overall_score:.1f}/100")
            
            # 评分进度条
            st.progress(overall_score / 100)
            
            # 各项指标对比
            st.markdown("##### 📊 指标对比")
            
            metrics = evaluation.get("metrics", {})
            baseline_metrics = {
                "efficiency_score": 75.0,
                "cost_score": 70.0,
                "delivery_score": 80.0,
                "quality_score": 85.0
            }
            
            import plotly.graph_objects as go
            
            categories = ['效率', '成本', '交期', '质量']
            current_values = [
                metrics.get('efficiency_score', 0),
                metrics.get('cost_score', 0),
                metrics.get('delivery_score', 0),
                metrics.get('quality_score', 0)
            ]
            baseline_values = [
                baseline_metrics['efficiency_score'],
                baseline_metrics['cost_score'],
                baseline_metrics['delivery_score'],
                baseline_metrics['quality_score']
            ]
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatterpolar(
                r=current_values,
                theta=categories,
                fill='toself',
                name='算法优化后',
                line_color='blue'
            ))
            
            fig.add_trace(go.Scatterpolar(
                r=baseline_values,
                theta=categories,
                fill='toself',
                name='基线水平',
                line_color='red'
            ))
            
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="性能指标雷达图"
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 改进分析
            st.markdown("##### 📈 改进分析")
            
            improvements = []
            for key, current in metrics.items():
                baseline = baseline_metrics.get(key, 0)
                improvement = current - baseline
                metric_name = key.replace('_score', '').replace('_', ' ').title()
                
                if improvement > 0:
                    improvements.append(f"✅ {metric_name}: 提升 {improvement:.1f} 分")
                elif improvement < 0:
                    improvements.append(f"❌ {metric_name}: 下降 {abs(improvement):.1f} 分")
                else:
                    improvements.append(f"➖ {metric_name}: 无变化")
            
            for improvement in improvements:
                st.write(improvement)
            
            # 预测信息
            predictions = evaluation.get("predictions", {})
            if predictions:
                st.markdown("##### 🔮 预测信息")
                for key, value in predictions.items():
                    st.write(f"**{key}**: {value}")
    
    else:
        st.info("请先生成计划以查看性能分析")
