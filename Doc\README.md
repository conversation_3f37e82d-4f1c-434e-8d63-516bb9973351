# 智能工厂生产管理规划系统 (Smart APS) - 文档总览

## 📋 项目简介

智能工厂生产管理规划系统（Smart APS）是一个现代化的生产调度优化平台，专为20-30人的小规模团队设计。系统通过整合传统优化算法、实时数据处理和本地/云端LLM的推理能力，实现智能化的生产规划和排程。

### 🎯 核心特性

- **智能化决策**：结合MILP优化算法与LLM推理，提供更准确的生产规划
- **灵活数据源**：支持邮件、Excel文件上传和自动数据提取
- **交互式前端**：基于Streamlit的丰富图表和用户交互界面
- **灵活配置**：高度可配置的数据源、约束条件和业务规则
- **本地部署**：支持Ollama本地LLM和Azure OpenAI云端服务

### 📈 预期效益

| 指标 | 预期提升 | 说明 |
|------|---------|------|
| 生产效率 | 15%-20% | 优化资源配置，减少设备空闲时间 |
| 计划准确率 | 提升至90%+ | 智能算法结合历史学习 |
| 响应速度 | 从小时级到分钟级 | 自动化计划生成和调整 |
| 人工工作量 | 减少50% | 自动化数据处理和计划生成 |

## 📚 文档结构

本项目文档采用模块化组织，涵盖项目的各个方面：

### 1. [项目概览与架构](./01_项目概览与架构.md)
- 项目整体介绍和价值主张
- 系统架构设计和技术选型
- 小规模部署架构和环境要求
- 项目结构和开发路线图

**适合读者**：项目经理、技术负责人、架构师

### 2. [需求分析与用户指南](./02_需求分析与用户指南.md)
- 业务需求分析和痛点识别
- 用户角色定义和用户故事
- 功能需求规格和验收标准
- 风险评估与应对策略

**适合读者**：产品经理、业务分析师、最终用户

### 3. [技术设计文档](./03_技术设计文档.md)
- 数据模型设计和数据库架构
- 文件上传和数据提取API设计
- 本地LLM和Azure OpenAI集成
- 灵活配置系统设计

**适合读者**：开发工程师、系统架构师、DBA

### 4. [开发实施指南](./04_开发实施指南.md)
- 小规模团队配置和技能要求
- 开发环境搭建和工具配置
- 开发流程和代码规范
- 质量保证和部署运维

**适合读者**：开发团队、DevOps工程师、测试工程师

### 5. [扩展与插件开发](./05_扩展与插件开发.md)
- 插件系统架构和设计理念
- 插件开发指南和示例代码
- 插件测试和发布流程
- 插件管理和安全机制

**适合读者**：插件开发者、第三方集成商

### 6. [技术评估与最佳实践](./06_技术评估与最佳实践.md)
- 技术选型评估和对比分析
- 架构设计最佳实践
- 性能优化策略和技巧
- 安全最佳实践和防护措施

**适合读者**：技术负责人、高级开发工程师、安全工程师

### 7. [Streamlit前端开发指南](./07_Streamlit前端开发指南.md)
- Streamlit技术栈和项目结构
- 文件上传和数据处理组件
- 交互式图表和可视化组件
- 用户界面设计最佳实践

**适合读者**：前端开发工程师、UI/UX设计师

## 🚀 快速开始

### 环境要求（小规模部署）

- **Python 3.9+**
- **MySQL 8.0+**
- **Redis 7.0+**
- **Ollama** (可选，本地LLM)
- **Docker 20.10+**

### 快速部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/smart-aps.git
cd smart-aps

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动数据库服务
docker-compose up -d mysql redis

# 4. 初始化数据库
python scripts/init_database.py

# 5. 启动后端API
uvicorn app.main:app --host 0.0.0.0 --port 8000

# 6. 启动Streamlit前端
streamlit run frontend/main.py

# 访问地址：
# - 前端界面: http://localhost:8501
# - API文档: http://localhost:8000/docs
```

### Ollama本地LLM配置（可选）

```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载模型
ollama pull llama2
ollama pull mistral

# 3. 启动Ollama服务
ollama serve

# 4. 配置环境变量
export OLLAMA_BASE_URL=http://localhost:11434
```

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│              前端展示层 (Streamlit + Plotly)                   │
├─────────────────────────────────────────────────────────────┤
│                    API服务层 (FastAPI)                        │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 文件上传     │ │ 数据源集成   │ │ 规划引擎     │ │ 算法模块     │ │
│  │ 邮件解析     │ │ Excel解析    │ │ 约束管理     │ │ MILP优化    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ LLM集成     │ │ 权限管理     │ │ 插件管理     │ │ 配置管理     │ │
│  │ Ollama/Azure│ │ 用户认证     │ │ 扩展支持     │ │ 灵活配置     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (SQLAlchemy)                     │
├─────────────────────────────────────────────────────────────┤
│              数据存储层 (MySQL + Redis)                        │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **Python 3.9+** - 主要开发语言
- **FastAPI** - 高性能Web框架
- **SQLAlchemy** - ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 7.0+** - 缓存和会话管理

### 前端技术
- **Streamlit** - 快速构建数据应用
- **Plotly + Altair** - 丰富的交互式图表
- **Streamlit-aggrid** - 高级表格组件
- **Bootstrap** - 响应式UI组件

### 算法与AI
- **PuLP** - 线性规划求解器
- **Scikit-learn** - 机器学习
- **Ollama** - 本地大型语言模型
- **Azure OpenAI** - 云端LLM服务
- **NumPy/Pandas** - 数据处理

### 文件处理
- **openpyxl** - Excel文件处理
- **email-parser** - 邮件解析
- **python-multipart** - 文件上传

## 📋 开发计划

### 第一阶段：核心功能 (8周)
- [x] 基础架构搭建
- [ ] 文件上传和数据提取
- [ ] 基础规划算法
- [ ] Streamlit界面

### 第二阶段：智能化 (6周)
- [ ] Ollama/Azure OpenAI集成
- [ ] 学习机制
- [ ] 高级可视化
- [ ] 插件系统

### 第三阶段：优化完善 (4周)
- [ ] 性能优化
- [ ] 安全加固
- [ ] 文档完善
- [ ] 用户培训

**总开发周期：18周**

## 🤝 贡献指南

### 开发流程

1. **Fork项目** 并创建功能分支
2. **遵循代码规范** 进行开发
3. **编写测试** 确保代码质量
4. **提交Pull Request** 进行代码审查

### 代码规范

- **Python**: 遵循PEP 8，使用Black格式化
- **Git提交**: 使用Conventional Commits格式

详细的开发规范请参考：[开发实施指南 - 开发流程与规范](./04_开发实施指南.md#开发流程与规范)

## 📞 联系方式

- **项目负责人**: [姓名] <<EMAIL>>
- **技术支持**: [技术团队邮箱]
- **问题反馈**: [GitHub Issues](https://github.com/your-org/smart-aps/issues)

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🔄 更新日志

### v1.0.0 (计划中)
- 核心生产规划功能
- 文件上传和数据提取
- Streamlit用户界面
- Ollama/Azure OpenAI集成

### v0.1.0 (当前)
- 项目初始化
- 基础架构搭建
- 文档框架

---

## 📖 文档导航

| 文档 | 描述 | 目标读者 |
|------|------|---------|
| [项目概览与架构](./01_项目概览与架构.md) | 系统整体设计和架构 | 技术负责人、架构师 |
| [需求分析与用户指南](./02_需求分析与用户指南.md) | 业务需求和用户故事 | 产品经理、业务分析师 |
| [技术设计文档](./03_技术设计文档.md) | 详细技术设计和实现 | 开发工程师、架构师 |
| [开发实施指南](./04_开发实施指南.md) | 开发流程和质量保证 | 开发团队、DevOps |
| [扩展与插件开发](./05_扩展与插件开发.md) | 插件系统和扩展开发 | 插件开发者 |
| [技术评估与最佳实践](./06_技术评估与最佳实践.md) | 技术选型和最佳实践 | 技术负责人、高级工程师 |
| [Streamlit前端开发指南](./07_Streamlit前端开发指南.md) | 前端开发和UI设计 | 前端开发工程师 |

**建议阅读顺序**：
1. 新用户：README → 项目概览与架构 → 需求分析与用户指南
2. 开发人员：技术设计文档 → 开发实施指南 → Streamlit前端开发指南
3. 扩展开发：扩展与插件开发 → 技术设计文档

---

*最后更新时间：2024年1月*
