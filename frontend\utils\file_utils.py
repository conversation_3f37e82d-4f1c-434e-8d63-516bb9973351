"""
文件处理工具函数
"""

import pandas as pd
import io
import email
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import streamlit as st
from typing import Dict, Any, Optional
import logging

from config.settings import AppConfig

logger = logging.getLogger(__name__)


def validate_file(uploaded_file) -> Dict[str, Any]:
    """验证上传的文件"""
    try:
        # 检查文件大小
        if uploaded_file.size > AppConfig.MAX_UPLOAD_SIZE:
            return {
                "valid": False,
                "message": f"文件大小超过限制 ({AppConfig.MAX_UPLOAD_SIZE // 1024 // 1024}MB)"
            }
        
        # 检查文件类型
        if uploaded_file.type not in AppConfig.ALLOWED_FILE_TYPES:
            return {
                "valid": False,
                "message": f"不支持的文件类型: {uploaded_file.type}"
            }
        
        # 检查文件名
        if not uploaded_file.name or len(uploaded_file.name.strip()) == 0:
            return {
                "valid": False,
                "message": "文件名不能为空"
            }
        
        # 特定文件类型验证
        validation_result = _validate_file_content(uploaded_file)
        if not validation_result["valid"]:
            return validation_result
        
        return {
            "valid": True,
            "message": "文件验证通过",
            "file_info": {
                "name": uploaded_file.name,
                "size": uploaded_file.size,
                "type": uploaded_file.type
            }
        }
        
    except Exception as e:
        logger.error(f"文件验证失败: {str(e)}")
        return {
            "valid": False,
            "message": f"文件验证失败: {str(e)}"
        }


def _validate_file_content(uploaded_file) -> Dict[str, Any]:
    """验证文件内容"""
    try:
        file_type = uploaded_file.type
        
        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        "application/vnd.ms-excel"]:
            return _validate_excel_file(uploaded_file)
        
        elif file_type == "text/csv":
            return _validate_csv_file(uploaded_file)
        
        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _validate_email_file(uploaded_file)
        
        else:
            return {"valid": True, "message": "文件类型验证通过"}
            
    except Exception as e:
        return {"valid": False, "message": f"文件内容验证失败: {str(e)}"}


def _validate_excel_file(uploaded_file) -> Dict[str, Any]:
    """验证Excel文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)
        
        # 尝试读取Excel文件
        excel_file = pd.ExcelFile(uploaded_file)
        
        if len(excel_file.sheet_names) == 0:
            return {"valid": False, "message": "Excel文件没有工作表"}
        
        # 检查是否有数据
        has_data = False
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=1)
            if not df.empty:
                has_data = True
                break
        
        if not has_data:
            return {"valid": False, "message": "Excel文件没有数据"}
        
        return {
            "valid": True,
            "message": f"Excel文件验证通过，包含 {len(excel_file.sheet_names)} 个工作表"
        }
        
    except Exception as e:
        return {"valid": False, "message": f"Excel文件格式错误: {str(e)}"}


def _validate_csv_file(uploaded_file) -> Dict[str, Any]:
    """验证CSV文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)
        
        # 尝试读取CSV文件
        df = pd.read_csv(uploaded_file, nrows=5)
        
        if df.empty:
            return {"valid": False, "message": "CSV文件没有数据"}
        
        if len(df.columns) < 2:
            return {"valid": False, "message": "CSV文件列数太少"}
        
        return {
            "valid": True,
            "message": f"CSV文件验证通过，包含 {len(df.columns)} 列"
        }
        
    except Exception as e:
        return {"valid": False, "message": f"CSV文件格式错误: {str(e)}"}


def _validate_email_file(uploaded_file) -> Dict[str, Any]:
    """验证邮件文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)
        
        # 读取邮件内容
        email_content = uploaded_file.read()
        
        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')
        
        # 尝试解析邮件
        msg = email.message_from_string(email_content)
        
        if not msg:
            return {"valid": False, "message": "邮件文件格式错误"}
        
        # 检查基本字段
        if not msg.get('Subject') and not msg.get('From'):
            return {"valid": False, "message": "邮件缺少基本信息"}
        
        return {
            "valid": True,
            "message": "邮件文件验证通过"
        }
        
    except Exception as e:
        return {"valid": False, "message": f"邮件文件格式错误: {str(e)}"}


def preview_file_content(uploaded_file, max_rows: int = 10) -> Optional[pd.DataFrame]:
    """预览文件内容"""
    try:
        file_type = uploaded_file.type
        
        # 重置文件指针
        uploaded_file.seek(0)
        
        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        "application/vnd.ms-excel"]:
            return _preview_excel_file(uploaded_file, max_rows)
        
        elif file_type == "text/csv":
            return _preview_csv_file(uploaded_file, max_rows)
        
        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _preview_email_file(uploaded_file)
        
        else:
            return None
            
    except Exception as e:
        logger.error(f"文件预览失败: {str(e)}")
        st.error(f"文件预览失败: {str(e)}")
        return None


def _preview_excel_file(uploaded_file, max_rows: int) -> Optional[pd.DataFrame]:
    """预览Excel文件"""
    try:
        excel_file = pd.ExcelFile(uploaded_file)
        
        # 读取第一个有数据的工作表
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=max_rows)
            if not df.empty:
                return df
        
        return None
        
    except Exception as e:
        logger.error(f"Excel预览失败: {str(e)}")
        return None


def _preview_csv_file(uploaded_file, max_rows: int) -> Optional[pd.DataFrame]:
    """预览CSV文件"""
    try:
        df = pd.read_csv(uploaded_file, nrows=max_rows)
        return df
        
    except Exception as e:
        logger.error(f"CSV预览失败: {str(e)}")
        return None


def _preview_email_file(uploaded_file) -> Optional[pd.DataFrame]:
    """预览邮件文件"""
    try:
        # 读取邮件内容
        email_content = uploaded_file.read()
        
        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')
        
        # 解析邮件
        msg = email.message_from_string(email_content)
        
        # 提取邮件信息
        email_info = {
            "字段": ["发件人", "收件人", "主题", "日期", "内容预览"],
            "值": [
                msg.get('From', ''),
                msg.get('To', ''),
                msg.get('Subject', ''),
                msg.get('Date', ''),
                _get_email_body_preview(msg)
            ]
        }
        
        return pd.DataFrame(email_info)
        
    except Exception as e:
        logger.error(f"邮件预览失败: {str(e)}")
        return None


def _get_email_body_preview(msg, max_length: int = 200) -> str:
    """获取邮件正文预览"""
    try:
        body = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    body = part.get_payload(decode=True)
                    if isinstance(body, bytes):
                        body = body.decode('utf-8', errors='ignore')
                    break
        else:
            body = msg.get_payload(decode=True)
            if isinstance(body, bytes):
                body = body.decode('utf-8', errors='ignore')
        
        # 截取预览长度
        if len(body) > max_length:
            body = body[:max_length] + "..."
        
        return body
        
    except Exception:
        return "无法获取邮件内容"


def extract_tables_from_file(uploaded_file) -> Dict[str, Any]:
    """从文件中提取表格数据"""
    try:
        file_type = uploaded_file.type
        
        # 重置文件指针
        uploaded_file.seek(0)
        
        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        "application/vnd.ms-excel"]:
            return _extract_excel_tables(uploaded_file)
        
        elif file_type == "text/csv":
            return _extract_csv_table(uploaded_file)
        
        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _extract_email_tables(uploaded_file)
        
        else:
            return {"success": False, "message": "不支持的文件类型"}
            
    except Exception as e:
        logger.error(f"表格提取失败: {str(e)}")
        return {"success": False, "message": f"表格提取失败: {str(e)}"}


def _extract_excel_tables(uploaded_file) -> Dict[str, Any]:
    """从Excel文件提取表格"""
    try:
        excel_file = pd.ExcelFile(uploaded_file)
        tables = []
        
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            if not df.empty:
                tables.append({
                    "sheet_name": sheet_name,
                    "table_name": sheet_name,
                    "data": df.to_dict('records'),
                    "columns": df.columns.tolist(),
                    "row_count": len(df),
                    "column_count": len(df.columns)
                })
        
        return {
            "success": True,
            "tables": tables,
            "total_tables": len(tables)
        }
        
    except Exception as e:
        return {"success": False, "message": f"Excel表格提取失败: {str(e)}"}


def _extract_csv_table(uploaded_file) -> Dict[str, Any]:
    """从CSV文件提取表格"""
    try:
        df = pd.read_csv(uploaded_file)
        
        table = {
            "sheet_name": "CSV数据",
            "table_name": "CSV数据",
            "data": df.to_dict('records'),
            "columns": df.columns.tolist(),
            "row_count": len(df),
            "column_count": len(df.columns)
        }
        
        return {
            "success": True,
            "tables": [table],
            "total_tables": 1
        }
        
    except Exception as e:
        return {"success": False, "message": f"CSV表格提取失败: {str(e)}"}


def _extract_email_tables(uploaded_file) -> Dict[str, Any]:
    """从邮件文件提取表格"""
    try:
        # 这里可以实现邮件中表格的提取逻辑
        # 暂时返回邮件基本信息
        email_content = uploaded_file.read()
        
        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')
        
        msg = email.message_from_string(email_content)
        
        email_data = {
            "发件人": msg.get('From', ''),
            "收件人": msg.get('To', ''),
            "主题": msg.get('Subject', ''),
            "日期": msg.get('Date', ''),
            "内容": _get_email_body_preview(msg, 500)
        }
        
        table = {
            "sheet_name": "邮件信息",
            "table_name": "邮件信息",
            "data": [email_data],
            "columns": list(email_data.keys()),
            "row_count": 1,
            "column_count": len(email_data)
        }
        
        return {
            "success": True,
            "tables": [table],
            "total_tables": 1
        }
        
    except Exception as e:
        return {"success": False, "message": f"邮件表格提取失败: {str(e)}"}


def get_file_type_display_name(mime_type: str) -> str:
    """获取文件类型显示名称"""
    return AppConfig.get_file_type_display_name(mime_type)


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / 1024 / 1024:.1f} MB"
    else:
        return f"{size_bytes / 1024 / 1024 / 1024:.1f} GB"
