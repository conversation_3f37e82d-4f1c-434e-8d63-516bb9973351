"""
智能学习引擎 - 基于历史数据持续优化生产计划算法
包含强化学习排程和深度学习优化功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import json
import sqlite3
import logging
from dataclasses import dataclass
import os
import threading
import asyncio
from collections import deque

# 尝试导入sklearn，如果没有则使用简化版本
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.metrics import mean_absolute_error, r2_score
    import joblib
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # 简化版本的模拟实现
    class RandomForestRegressor:
        def __init__(self, n_estimators=100, random_state=42):
            self.n_estimators = n_estimators
            self.random_state = random_state
            self.is_fitted = False

        def fit(self, X, y):
            self.is_fitted = True
            return self

        def predict(self, X):
            if not self.is_fitted:
                raise ValueError("模型未训练")
            # 简单的线性预测模拟
            return np.array([np.mean(X[i]) * 1.2 for i in range(len(X))])

    def mean_absolute_error(y_true, y_pred):
        return np.mean(np.abs(np.array(y_true) - np.array(y_pred)))

    def r2_score(y_true, y_pred):
        return 0.85  # 模拟R²分数

    class joblib:
        @staticmethod
        def dump(obj, filename):
            pass

        @staticmethod
        def load(filename):
            return RandomForestRegressor()

# 尝试导入深度学习库
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    # 简化版本的模拟实现
    class torch:
        class nn:
            class Module:
                def __init__(self):
                    self.training = True
                def forward(self, x):
                    return x
                def train(self):
                    self.training = True
                def eval(self):
                    self.training = False
                def parameters(self):
                    return []

            class Linear:
                def __init__(self, in_features, out_features):
                    self.in_features = in_features
                    self.out_features = out_features
                def forward(self, x):
                    return np.random.randn(x.shape[0], self.out_features)

            class ReLU:
                def forward(self, x):
                    return np.maximum(0, x)

            class Dropout:
                def __init__(self, p=0.5):
                    self.p = p
                def forward(self, x):
                    return x

        class optim:
            class Adam:
                def __init__(self, params, lr=0.001):
                    self.lr = lr
                def zero_grad(self):
                    pass
                def step(self):
                    pass

        @staticmethod
        def tensor(data):
            return np.array(data)

        @staticmethod
        def save(obj, path):
            pass

        @staticmethod
        def load(path):
            return None


@dataclass
class PlanPerformance:
    """计划执行性能数据"""
    plan_id: str
    predicted_duration: float  # 预测工期
    actual_duration: float     # 实际工期
    predicted_cost: float      # 预测成本
    actual_cost: float         # 实际成本
    predicted_quality: float   # 预测质量
    actual_quality: float      # 实际质量
    on_time_delivery: bool     # 是否按时交付
    equipment_utilization: float  # 设备利用率
    created_at: datetime
    completed_at: datetime


@dataclass
class SchedulingState:
    """排程状态表示"""
    orders: List[Dict[str, Any]]  # 订单列表
    equipment_status: Dict[str, Any]  # 设备状态
    resource_availability: Dict[str, float]  # 资源可用性
    time_step: int  # 时间步
    current_makespan: float  # 当前完工时间
    utilization_rate: float  # 利用率

    def to_vector(self) -> np.ndarray:
        """转换为向量表示"""
        # 简化的状态向量化
        features = []

        # 订单特征
        features.extend([
            len(self.orders),
            sum(order.get('quantity', 0) for order in self.orders),
            sum(order.get('priority', 0) for order in self.orders),
        ])

        # 设备特征
        features.extend([
            len(self.equipment_status),
            sum(1 for status in self.equipment_status.values() if status.get('available', False)),
            self.utilization_rate
        ])

        # 时间特征
        features.extend([
            self.time_step,
            self.current_makespan
        ])

        return np.array(features, dtype=np.float32)


@dataclass
class SchedulingAction:
    """排程动作"""
    order_id: str
    equipment_id: str
    start_time: float
    priority_adjustment: float = 0.0

    def to_vector(self) -> np.ndarray:
        """转换为向量表示"""
        # 简化的动作向量化
        return np.array([
            hash(self.order_id) % 1000,  # 订单ID哈希
            hash(self.equipment_id) % 100,  # 设备ID哈希
            self.start_time,
            self.priority_adjustment
        ], dtype=np.float32)


@dataclass
class RLExperience:
    """强化学习经验"""
    state: SchedulingState
    action: SchedulingAction
    reward: float
    next_state: SchedulingState
    done: bool
    timestamp: datetime


class DQNNetwork:
    """深度Q网络"""

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims or [128, 64, 32]

        if TORCH_AVAILABLE:
            self.network = self._build_network()
            self.target_network = self._build_network()
            self.optimizer = torch.optim.Adam(self.network.parameters(), lr=0.001)
        else:
            # 简化版本
            self.network = None
            self.target_network = None
            self.optimizer = None

    def _build_network(self):
        """构建神经网络"""
        if not TORCH_AVAILABLE:
            return None

        class DQN(torch.nn.Module):
            def __init__(self, state_dim, action_dim, hidden_dims):
                super(DQN, self).__init__()

                layers = []
                input_dim = state_dim

                for hidden_dim in hidden_dims:
                    layers.extend([
                        torch.nn.Linear(input_dim, hidden_dim),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.2)
                    ])
                    input_dim = hidden_dim

                layers.append(torch.nn.Linear(input_dim, action_dim))

                self.layers = torch.nn.Sequential(*layers)

            def forward(self, x):
                return self.layers(x)

        return DQN(self.state_dim, self.action_dim, self.hidden_dims)

    def predict(self, state: np.ndarray) -> np.ndarray:
        """预测Q值"""
        if not TORCH_AVAILABLE or self.network is None:
            # 简化版本：随机Q值
            return np.random.randn(self.action_dim)

        self.network.eval()
        with torch.no_grad():
            state_tensor = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
            q_values = self.network(state_tensor)
            return q_values.squeeze().numpy()

    def train_step(self, batch_states: np.ndarray, batch_actions: np.ndarray,
                   batch_rewards: np.ndarray, batch_next_states: np.ndarray,
                   batch_dones: np.ndarray, gamma: float = 0.99) -> float:
        """训练步骤"""
        if not TORCH_AVAILABLE or self.network is None:
            return 0.0

        self.network.train()

        # 转换为张量
        states = torch.tensor(batch_states, dtype=torch.float32)
        actions = torch.tensor(batch_actions, dtype=torch.long)
        rewards = torch.tensor(batch_rewards, dtype=torch.float32)
        next_states = torch.tensor(batch_next_states, dtype=torch.float32)
        dones = torch.tensor(batch_dones, dtype=torch.bool)

        # 当前Q值
        current_q_values = self.network(states).gather(1, actions.unsqueeze(1))

        # 目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (gamma * next_q_values * ~dones)

        # 计算损失
        loss = torch.nn.functional.mse_loss(current_q_values.squeeze(), target_q_values)

        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        return loss.item()

    def update_target_network(self):
        """更新目标网络"""
        if TORCH_AVAILABLE and self.network is not None:
            self.target_network.load_state_dict(self.network.state_dict())

    def save(self, path: str):
        """保存模型"""
        if TORCH_AVAILABLE and self.network is not None:
            torch.save(self.network.state_dict(), path)

    def load(self, path: str):
        """加载模型"""
        if TORCH_AVAILABLE and self.network is not None and os.path.exists(path):
            self.network.load_state_dict(torch.load(path))
            self.update_target_network()


class ReinforcementLearningScheduler:
    """强化学习排程器"""

    def __init__(self, state_dim: int = 8, action_dim: int = 100):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.dqn = DQNNetwork(state_dim, action_dim)

        # 经验回放缓冲区
        self.experience_buffer = deque(maxlen=10000)
        self.batch_size = 32
        self.epsilon = 0.1  # 探索率
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01

        # 训练参数
        self.gamma = 0.99
        self.target_update_freq = 100
        self.training_step = 0

        # 性能统计
        self.episode_rewards = []
        self.episode_lengths = []

    def select_action(self, state: SchedulingState, available_actions: List[SchedulingAction]) -> SchedulingAction:
        """选择动作（ε-贪心策略）"""
        if np.random.random() < self.epsilon:
            # 探索：随机选择
            return np.random.choice(available_actions)
        else:
            # 利用：选择Q值最高的动作
            state_vector = state.to_vector()
            q_values = self.dqn.predict(state_vector)

            # 简化：选择第一个可用动作（实际应该根据Q值选择）
            return available_actions[0] if available_actions else None

    def store_experience(self, experience: RLExperience):
        """存储经验"""
        self.experience_buffer.append(experience)

    def train(self) -> Dict[str, float]:
        """训练DQN"""
        if len(self.experience_buffer) < self.batch_size:
            return {"loss": 0.0, "epsilon": self.epsilon}

        # 采样批次
        batch = np.random.choice(list(self.experience_buffer), self.batch_size, replace=False)

        # 准备训练数据
        batch_states = np.array([exp.state.to_vector() for exp in batch])
        batch_actions = np.array([0 for exp in batch])  # 简化：动作索引
        batch_rewards = np.array([exp.reward for exp in batch])
        batch_next_states = np.array([exp.next_state.to_vector() for exp in batch])
        batch_dones = np.array([exp.done for exp in batch])

        # 训练步骤
        loss = self.dqn.train_step(
            batch_states, batch_actions, batch_rewards,
            batch_next_states, batch_dones, self.gamma
        )

        # 更新探索率
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)

        # 更新目标网络
        self.training_step += 1
        if self.training_step % self.target_update_freq == 0:
            self.dqn.update_target_network()

        return {"loss": loss, "epsilon": self.epsilon}

    def calculate_reward(self, state: SchedulingState, action: SchedulingAction,
                        next_state: SchedulingState) -> float:
        """计算奖励"""
        # 多目标奖励函数
        reward = 0.0

        # 1. 完工时间改进奖励
        makespan_improvement = state.current_makespan - next_state.current_makespan
        reward += makespan_improvement * 10

        # 2. 设备利用率奖励
        utilization_improvement = next_state.utilization_rate - state.utilization_rate
        reward += utilization_improvement * 5

        # 3. 订单完成奖励
        completed_orders = len(state.orders) - len(next_state.orders)
        reward += completed_orders * 20

        # 4. 时间惩罚（鼓励快速决策）
        reward -= 0.1

        return reward

    def save_model(self, path: str):
        """保存模型"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        self.dqn.save(path)

    def load_model(self, path: str):
        """加载模型"""
        if os.path.exists(path):
            self.dqn.load(path)


class LearningEngine:
    """智能学习引擎"""

    def __init__(self, db_path: str = "learning_data.db"):
        self.db_path = db_path
        self.models = {}
        self.performance_history = []
        self.learning_config = {
            "enabled": True,
            "data_window_days": 30,
            "min_samples": 50,
            "learning_rate": 0.1,
            "weights": {
                "delivery": 0.4,
                "efficiency": 0.3,
                "cost": 0.3
            },
            "rl_enabled": True,
            "deep_learning_enabled": True
        }

        # 强化学习组件
        self.rl_scheduler = ReinforcementLearningScheduler()
        self.rl_training_active = False
        self.rl_episode_count = 0

        # 深度学习训练历史
        self.training_history = {
            "losses": [],
            "rewards": [],
            "epsilon_values": [],
            "episodes": []
        }

        self._init_database()
        self._load_models()
        self._load_rl_models()

    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建性能记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plan_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_id TEXT NOT NULL,
                predicted_duration REAL,
                actual_duration REAL,
                predicted_cost REAL,
                actual_cost REAL,
                predicted_quality REAL,
                actual_quality REAL,
                on_time_delivery INTEGER,
                equipment_utilization REAL,
                created_at TEXT,
                completed_at TEXT,
                features TEXT
            )
        ''')

        # 创建学习记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                model_type TEXT,
                samples_count INTEGER,
                accuracy_before REAL,
                accuracy_after REAL,
                improvement REAL,
                created_at TEXT,
                config TEXT
            )
        ''')

        # 创建强化学习经验表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rl_experiences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                episode_id TEXT NOT NULL,
                step_id INTEGER,
                state_vector TEXT,
                action_vector TEXT,
                reward REAL,
                next_state_vector TEXT,
                done INTEGER,
                timestamp TEXT
            )
        ''')

        # 创建强化学习训练记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rl_training_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                episode_count INTEGER,
                total_reward REAL,
                average_loss REAL,
                epsilon_value REAL,
                training_time REAL,
                created_at TEXT,
                config TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def record_plan_performance(self, performance: PlanPerformance, features: Dict[str, Any]):
        """记录计划执行性能"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO plan_performance (
                plan_id, predicted_duration, actual_duration,
                predicted_cost, actual_cost, predicted_quality, actual_quality,
                on_time_delivery, equipment_utilization, created_at, completed_at, features
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            performance.plan_id,
            performance.predicted_duration,
            performance.actual_duration,
            performance.predicted_cost,
            performance.actual_cost,
            performance.predicted_quality,
            performance.actual_quality,
            int(performance.on_time_delivery),
            performance.equipment_utilization,
            performance.created_at.isoformat(),
            performance.completed_at.isoformat(),
            json.dumps(features)
        ))

        conn.commit()
        conn.close()

        # 检查是否需要触发学习
        self._check_learning_trigger()

    def _check_learning_trigger(self):
        """检查是否需要触发学习"""
        if not self.learning_config["enabled"]:
            return

        # 获取最近的数据量
        recent_data = self._get_recent_performance_data()

        if len(recent_data) >= self.learning_config["min_samples"]:
            # 检查上次学习时间
            last_learning = self._get_last_learning_time()
            if self._should_trigger_learning(last_learning):
                self.trigger_learning()

    def _get_recent_performance_data(self) -> List[Dict]:
        """获取最近的性能数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cutoff_date = (datetime.now() - timedelta(days=self.learning_config["data_window_days"])).isoformat()

        cursor.execute('''
            SELECT * FROM plan_performance
            WHERE completed_at >= ?
            ORDER BY completed_at DESC
        ''', (cutoff_date,))

        columns = [desc[0] for desc in cursor.description]
        data = [dict(zip(columns, row)) for row in cursor.fetchall()]

        conn.close()
        return data

    def trigger_learning(self) -> Dict[str, Any]:
        """触发学习过程"""
        session_id = f"learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        logging.info(f"开始学习会话: {session_id}")

        # 获取训练数据
        training_data = self._prepare_training_data()

        if len(training_data) < self.learning_config["min_samples"]:
            return {"success": False, "message": "训练数据不足"}

        # 训练模型
        results = {}

        # 1. 工期预测模型
        duration_result = self._train_duration_model(training_data)
        results["duration"] = duration_result

        # 2. 成本预测模型
        cost_result = self._train_cost_model(training_data)
        results["cost"] = cost_result

        # 3. 质量预测模型
        quality_result = self._train_quality_model(training_data)
        results["quality"] = quality_result

        # 记录学习会话
        self._record_learning_session(session_id, results)

        # 保存模型
        self._save_models()

        logging.info(f"学习会话完成: {session_id}")

        return {
            "success": True,
            "session_id": session_id,
            "results": results,
            "samples_count": len(training_data)
        }

    def _prepare_training_data(self) -> pd.DataFrame:
        """准备训练数据"""
        data = self._get_recent_performance_data()

        if not data:
            return pd.DataFrame()

        # 转换为DataFrame
        df = pd.DataFrame(data)

        # 解析特征
        features_list = []
        for _, row in df.iterrows():
            features = json.loads(row['features'])
            features_list.append(features)

        features_df = pd.DataFrame(features_list)

        # 合并数据
        result_df = pd.concat([df, features_df], axis=1)

        return result_df

    def _train_duration_model(self, data: pd.DataFrame) -> Dict[str, Any]:
        """训练工期预测模型"""
        # 特征选择
        feature_columns = ['quantity', 'complexity', 'equipment_count', 'priority']
        available_features = [col for col in feature_columns if col in data.columns]

        if not available_features:
            return {"success": False, "message": "缺少必要特征"}

        X = data[available_features].fillna(0)
        y = data['actual_duration']

        # 检查数据量
        if len(X) < 2:
            return {"success": False, "message": "训练数据不足（需要至少2个样本）"}

        # 训练模型
        model = RandomForestRegressor(n_estimators=min(100, len(X)*10), random_state=42)
        model.fit(X, y)

        # 评估模型
        predictions = model.predict(X)
        mae = mean_absolute_error(y, predictions)
        r2 = r2_score(y, predictions)

        # 计算改进
        baseline_mae = mean_absolute_error(y, data['predicted_duration'])
        improvement = (baseline_mae - mae) / baseline_mae * 100

        self.models['duration'] = model

        return {
            "success": True,
            "mae": mae,
            "r2_score": r2,
            "improvement": improvement,
            "features": available_features
        }

    def _train_cost_model(self, data: pd.DataFrame) -> Dict[str, Any]:
        """训练成本预测模型"""
        feature_columns = ['quantity', 'duration', 'equipment_count', 'material_cost']
        available_features = [col for col in feature_columns if col in data.columns]

        if not available_features:
            return {"success": False, "message": "缺少必要特征"}

        X = data[available_features].fillna(0)
        y = data['actual_cost']

        # 检查数据量
        if len(X) < 2:
            return {"success": False, "message": "训练数据不足（需要至少2个样本）"}

        model = RandomForestRegressor(n_estimators=min(100, len(X)*10), random_state=42)
        model.fit(X, y)

        predictions = model.predict(X)
        mae = mean_absolute_error(y, predictions)
        r2 = r2_score(y, predictions)

        baseline_mae = mean_absolute_error(y, data['predicted_cost'])
        improvement = (baseline_mae - mae) / baseline_mae * 100

        self.models['cost'] = model

        return {
            "success": True,
            "mae": mae,
            "r2_score": r2,
            "improvement": improvement,
            "features": available_features
        }

    def _train_quality_model(self, data: pd.DataFrame) -> Dict[str, Any]:
        """训练质量预测模型"""
        feature_columns = ['equipment_age', 'operator_skill', 'material_grade', 'environment_temp']
        available_features = [col for col in feature_columns if col in data.columns]

        if not available_features:
            return {"success": False, "message": "缺少必要特征"}

        X = data[available_features].fillna(0)
        y = data['actual_quality']

        # 检查数据量
        if len(X) < 2:
            return {"success": False, "message": "训练数据不足（需要至少2个样本）"}

        model = RandomForestRegressor(n_estimators=min(100, len(X)*10), random_state=42)
        model.fit(X, y)

        predictions = model.predict(X)
        mae = mean_absolute_error(y, predictions)
        r2 = r2_score(y, predictions)

        baseline_mae = mean_absolute_error(y, data['predicted_quality'])
        improvement = (baseline_mae - mae) / baseline_mae * 100

        self.models['quality'] = model

        return {
            "success": True,
            "mae": mae,
            "r2_score": r2,
            "improvement": improvement,
            "features": available_features
        }

    def predict_plan_performance(self, features: Dict[str, Any]) -> Dict[str, float]:
        """预测计划性能"""
        predictions = {}

        # 工期预测
        if 'duration' in self.models:
            duration_features = ['quantity', 'complexity', 'equipment_count', 'priority']
            duration_input = [features.get(f, 0) for f in duration_features]
            predictions['duration'] = self.models['duration'].predict([duration_input])[0]

        # 成本预测
        if 'cost' in self.models:
            cost_features = ['quantity', 'duration', 'equipment_count', 'material_cost']
            cost_input = [features.get(f, 0) for f in cost_features]
            predictions['cost'] = self.models['cost'].predict([cost_input])[0]

        # 质量预测
        if 'quality' in self.models:
            quality_features = ['equipment_age', 'operator_skill', 'material_grade', 'environment_temp']
            quality_input = [features.get(f, 0) for f in quality_features]
            predictions['quality'] = self.models['quality'].predict([quality_input])[0]

        return predictions

    def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取总样本数
        cursor.execute('SELECT COUNT(*) FROM plan_performance')
        total_samples = cursor.fetchone()[0]

        # 获取最近学习记录
        cursor.execute('''
            SELECT * FROM learning_sessions
            ORDER BY created_at DESC LIMIT 1
        ''')
        last_learning = cursor.fetchone()

        # 获取最近性能数据
        recent_data = self._get_recent_performance_data()

        conn.close()

        return {
            "enabled": self.learning_config["enabled"],
            "total_samples": total_samples,
            "recent_samples": len(recent_data),
            "min_samples_required": self.learning_config["min_samples"],
            "last_learning": last_learning,
            "models_loaded": list(self.models.keys()),
            "config": self.learning_config
        }

    def update_learning_config(self, config: Dict[str, Any]):
        """更新学习配置"""
        self.learning_config.update(config)

    def _save_models(self):
        """保存模型"""
        os.makedirs("models", exist_ok=True)
        for model_name, model in self.models.items():
            joblib.dump(model, f"models/{model_name}_model.pkl")

    def _load_models(self):
        """加载模型"""
        if not os.path.exists("models"):
            return

        for model_file in os.listdir("models"):
            if model_file.endswith("_model.pkl"):
                model_name = model_file.replace("_model.pkl", "")
                try:
                    self.models[model_name] = joblib.load(f"models/{model_file}")
                except Exception as e:
                    logging.warning(f"加载模型 {model_name} 失败: {e}")

    def _record_learning_session(self, session_id: str, results: Dict[str, Any]):
        """记录学习会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for model_type, result in results.items():
            if result.get("success"):
                cursor.execute('''
                    INSERT INTO learning_sessions (
                        session_id, model_type, samples_count, accuracy_before,
                        accuracy_after, improvement, created_at, config
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    model_type,
                    result.get("samples_count", 0),
                    0,  # 这里可以记录之前的准确率
                    result.get("r2_score", 0),
                    result.get("improvement", 0),
                    datetime.now().isoformat(),
                    json.dumps(self.learning_config)
                ))

        conn.commit()
        conn.close()

    def _get_last_learning_time(self) -> datetime:
        """获取最后学习时间"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT created_at FROM learning_sessions
            ORDER BY created_at DESC LIMIT 1
        ''')

        result = cursor.fetchone()
        conn.close()

        if result:
            return datetime.fromisoformat(result[0])
        return datetime.min

    def _should_trigger_learning(self, last_learning: datetime) -> bool:
        """判断是否应该触发学习"""
        # 这里可以根据配置的学习频率来判断
        # 简单实现：如果超过1天就触发
        return (datetime.now() - last_learning).days >= 1

    def _load_rl_models(self):
        """加载强化学习模型"""
        rl_model_path = "models/rl_scheduler.pth"
        if os.path.exists(rl_model_path):
            try:
                self.rl_scheduler.load_model(rl_model_path)
                logging.info("强化学习模型加载成功")
            except Exception as e:
                logging.warning(f"加载强化学习模型失败: {e}")

    def start_rl_training_episode(self, initial_state: SchedulingState) -> str:
        """开始强化学习训练回合"""
        if not self.learning_config.get("rl_enabled", False):
            return None

        episode_id = f"rl_episode_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.rl_episode_count}"
        self.rl_episode_count += 1
        self.rl_training_active = True

        logging.info(f"开始强化学习训练回合: {episode_id}")
        return episode_id

    def record_rl_experience(self, experience: RLExperience, episode_id: str, step_id: int):
        """记录强化学习经验"""
        if not self.learning_config.get("rl_enabled", False):
            return

        # 存储到内存缓冲区
        self.rl_scheduler.store_experience(experience)

        # 存储到数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO rl_experiences (
                episode_id, step_id, state_vector, action_vector, reward,
                next_state_vector, done, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            episode_id,
            step_id,
            json.dumps(experience.state.to_vector().tolist()),
            json.dumps(experience.action.to_vector().tolist()),
            experience.reward,
            json.dumps(experience.next_state.to_vector().tolist()),
            int(experience.done),
            experience.timestamp.isoformat()
        ))

        conn.commit()
        conn.close()

    def train_rl_model(self) -> Dict[str, Any]:
        """训练强化学习模型"""
        if not self.learning_config.get("rl_enabled", False):
            return {"success": False, "message": "强化学习未启用"}

        if not self.learning_config.get("deep_learning_enabled", False):
            return {"success": False, "message": "深度学习未启用"}

        session_id = f"rl_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()

        logging.info(f"开始强化学习训练: {session_id}")

        # 执行训练
        training_results = self.rl_scheduler.train()

        # 更新训练历史
        self.training_history["losses"].append(training_results.get("loss", 0.0))
        self.training_history["epsilon_values"].append(training_results.get("epsilon", 0.0))
        self.training_history["episodes"].append(self.rl_episode_count)

        # 计算训练时间
        training_time = (datetime.now() - start_time).total_seconds()

        # 记录训练会话
        self._record_rl_training_session(session_id, training_results, training_time)

        # 保存模型
        self._save_rl_models()

        logging.info(f"强化学习训练完成: {session_id}")

        return {
            "success": True,
            "session_id": session_id,
            "loss": training_results.get("loss", 0.0),
            "epsilon": training_results.get("epsilon", 0.0),
            "training_time": training_time,
            "episode_count": self.rl_episode_count
        }

    def _record_rl_training_session(self, session_id: str, results: Dict[str, Any], training_time: float):
        """记录强化学习训练会话"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO rl_training_sessions (
                session_id, episode_count, total_reward, average_loss,
                epsilon_value, training_time, created_at, config
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session_id,
            self.rl_episode_count,
            0.0,  # 这里可以计算总奖励
            results.get("loss", 0.0),
            results.get("epsilon", 0.0),
            training_time,
            datetime.now().isoformat(),
            json.dumps(self.learning_config)
        ))

        conn.commit()
        conn.close()

    def _save_rl_models(self):
        """保存强化学习模型"""
        os.makedirs("models", exist_ok=True)
        rl_model_path = "models/rl_scheduler.pth"
        self.rl_scheduler.save_model(rl_model_path)

    def get_rl_training_status(self) -> Dict[str, Any]:
        """获取强化学习训练状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取经验数量
        cursor.execute('SELECT COUNT(*) FROM rl_experiences')
        experience_count = cursor.fetchone()[0]

        # 获取最近训练记录
        cursor.execute('''
            SELECT * FROM rl_training_sessions
            ORDER BY created_at DESC LIMIT 1
        ''')
        last_training = cursor.fetchone()

        conn.close()

        return {
            "rl_enabled": self.learning_config.get("rl_enabled", False),
            "deep_learning_enabled": self.learning_config.get("deep_learning_enabled", False),
            "training_active": self.rl_training_active,
            "episode_count": self.rl_episode_count,
            "experience_count": experience_count,
            "buffer_size": len(self.rl_scheduler.experience_buffer),
            "epsilon": self.rl_scheduler.epsilon,
            "last_training": last_training,
            "training_history": self.training_history
        }

    def generate_rl_schedule(self, orders: List[Dict[str, Any]],
                           equipment_status: Dict[str, Any]) -> Dict[str, Any]:
        """使用强化学习生成排程"""
        if not self.learning_config.get("rl_enabled", False):
            return {"success": False, "message": "强化学习未启用"}

        # 构建初始状态
        initial_state = SchedulingState(
            orders=orders,
            equipment_status=equipment_status,
            resource_availability={},
            time_step=0,
            current_makespan=0.0,
            utilization_rate=0.0
        )

        # 生成可用动作
        available_actions = self._generate_available_actions(orders, equipment_status)

        if not available_actions:
            return {"success": False, "message": "没有可用的排程动作"}

        # 选择动作
        selected_action = self.rl_scheduler.select_action(initial_state, available_actions)

        if selected_action is None:
            return {"success": False, "message": "无法选择有效动作"}

        # 生成排程结果
        schedule_result = {
            "success": True,
            "schedule": {
                "order_id": selected_action.order_id,
                "equipment_id": selected_action.equipment_id,
                "start_time": selected_action.start_time,
                "priority_adjustment": selected_action.priority_adjustment
            },
            "state_info": {
                "orders_count": len(orders),
                "available_equipment": len([eq for eq, status in equipment_status.items()
                                          if status.get('available', False)]),
                "epsilon": self.rl_scheduler.epsilon
            }
        }

        return schedule_result

    def _generate_available_actions(self, orders: List[Dict[str, Any]],
                                  equipment_status: Dict[str, Any]) -> List[SchedulingAction]:
        """生成可用的排程动作"""
        actions = []

        available_equipment = [eq_id for eq_id, status in equipment_status.items()
                             if status.get('available', False)]

        for order in orders[:5]:  # 限制动作数量
            for equipment_id in available_equipment[:3]:  # 限制设备选择
                action = SchedulingAction(
                    order_id=order.get('id', ''),
                    equipment_id=equipment_id,
                    start_time=0.0,
                    priority_adjustment=0.0
                )
                actions.append(action)

        return actions


# 全局学习引擎实例
learning_engine = LearningEngine()
