"""
验证强化学习功能
"""

import sys
import os
import traceback

# 添加服务路径
sys.path.append('services')

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        from learning_engine import (
            SchedulingState, SchedulingAction, RLExperience,
            DQNNetwork, ReinforcementLearningScheduler, learning_engine
        )
        print("✅ learning_engine 导入成功")
    except Exception as e:
        print(f"❌ learning_engine 导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        from reinforcement_learning_service import rl_scheduling_service, SchedulingEnvironment
        print("✅ reinforcement_learning_service 导入成功")
    except Exception as e:
        print(f"❌ reinforcement_learning_service 导入失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from learning_engine import SchedulingState, SchedulingAction, learning_engine
        
        # 测试状态创建
        orders = [
            {"id": "ORD001", "quantity": 100, "priority": 1},
            {"id": "ORD002", "quantity": 150, "priority": 2}
        ]
        
        equipment = {
            "L01": {"available": True, "capacity": 200},
            "L02": {"available": True, "capacity": 180}
        }
        
        state = SchedulingState(
            orders=orders,
            equipment_status=equipment,
            resource_availability={},
            time_step=0,
            current_makespan=0.0,
            utilization_rate=0.0
        )
        
        print(f"✅ 状态创建成功: {len(state.orders)} 个订单")
        
        # 测试向量化
        vector = state.to_vector()
        print(f"✅ 状态向量化成功: 维度 {len(vector)}")
        
        # 测试动作创建
        action = SchedulingAction(
            order_id="ORD001",
            equipment_id="L01",
            start_time=0.0
        )
        
        action_vector = action.to_vector()
        print(f"✅ 动作创建成功: 维度 {len(action_vector)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_rl_status():
    """测试强化学习状态"""
    print("\n📊 测试强化学习状态...")
    
    try:
        from learning_engine import learning_engine
        
        # 获取强化学习状态
        rl_status = learning_engine.get_rl_training_status()
        
        print(f"✅ 强化学习状态获取成功:")
        print(f"   - RL启用: {rl_status['rl_enabled']}")
        print(f"   - 深度学习启用: {rl_status['deep_learning_enabled']}")
        print(f"   - 训练回合: {rl_status['episode_count']}")
        print(f"   - 经验数量: {rl_status['experience_count']}")
        print(f"   - 探索率: {rl_status['epsilon']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 强化学习状态测试失败: {e}")
        traceback.print_exc()
        return False

def test_dqn_network():
    """测试DQN网络"""
    print("\n🧠 测试DQN网络...")
    
    try:
        from learning_engine import DQNNetwork
        import numpy as np
        
        # 创建DQN网络
        dqn = DQNNetwork(state_dim=8, action_dim=100)
        print(f"✅ DQN网络创建成功: {dqn.state_dim}维输入, {dqn.action_dim}维输出")
        
        # 测试预测
        state_vector = np.random.randn(8)
        q_values = dqn.predict(state_vector)
        print(f"✅ DQN预测成功: Q值维度 {len(q_values)}")
        
        return True
        
    except Exception as e:
        print(f"❌ DQN网络测试失败: {e}")
        traceback.print_exc()
        return False

def test_rl_service():
    """测试强化学习服务"""
    print("\n🚀 测试强化学习服务...")
    
    try:
        from reinforcement_learning_service import rl_scheduling_service
        
        # 测试环境设置
        orders = [
            {"id": "ORD001", "quantity": 100, "priority": 1},
            {"id": "ORD002", "quantity": 150, "priority": 2}
        ]
        
        equipment = {
            "L01": {"available": True, "capacity": 200},
            "L02": {"available": True, "capacity": 180}
        }
        
        success = rl_scheduling_service.setup_environment(orders, equipment)
        print(f"✅ 环境设置成功: {success}")
        
        # 测试训练状态
        status = rl_scheduling_service.get_training_status()
        print(f"✅ 训练状态获取成功: 环境就绪 {status['environment_ready']}")
        
        # 测试排程生成
        result = rl_scheduling_service.generate_optimized_schedule(orders, equipment)
        print(f"✅ 排程生成测试: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 强化学习服务测试失败: {e}")
        traceback.print_exc()
        return False

def test_learning_engine_integration():
    """测试学习引擎集成"""
    print("\n🔗 测试学习引擎集成...")
    
    try:
        from learning_engine import learning_engine
        
        # 测试配置更新
        learning_engine.update_learning_config({
            "rl_enabled": True,
            "deep_learning_enabled": True
        })
        print("✅ 配置更新成功")
        
        # 测试强化学习排程生成
        orders = [{"id": "ORD001", "quantity": 100, "priority": 1}]
        equipment = {"L01": {"available": True, "capacity": 200}}
        
        result = learning_engine.generate_rl_schedule(orders, equipment)
        print(f"✅ 强化学习排程生成: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 学习引擎集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧠 强化学习功能验证开始")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_rl_status,
        test_dqn_network,
        test_rl_service,
        test_learning_engine_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有强化学习功能验证通过！")
        print("\n📋 功能清单:")
        print("✅ 深度Q网络 (DQN)")
        print("✅ 强化学习调度器")
        print("✅ 经验回放机制")
        print("✅ 智能排程生成")
        print("✅ 训练监控")
        print("✅ 性能分析")
        
        print("\n🚀 下一步:")
        print("1. 访问 '强化学习排程' 页面开始使用")
        print("2. 在 '算法学习中心' 监控训练进度")
        print("3. 查看 '强化学习排程指南.md' 了解详细用法")
        
    else:
        print("⚠️ 部分功能需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
