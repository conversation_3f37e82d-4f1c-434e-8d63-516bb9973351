"""
前端应用配置
"""

import os
from typing import List


class AppConfig:
    """应用配置类"""
    
    # 基础配置
    APP_NAME = "Smart APS"
    VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # API配置
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
    API_TIMEOUT = int(os.getenv("API_TIMEOUT", "30"))
    
    # 文件上传配置
    MAX_UPLOAD_SIZE = int(os.getenv("MAX_UPLOAD_SIZE", "200")) * 1024 * 1024  # MB转字节
    ALLOWED_FILE_TYPES = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # xlsx
        "application/vnd.ms-excel",  # xls
        "text/csv",  # csv
        "message/rfc822",  # eml
        "application/vnd.ms-outlook"  # msg
    ]
    
    # LLM配置
    DEFAULT_LLM_SERVICE = os.getenv("DEFAULT_LLM_SERVICE", "ollama")
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    
    # 缓存配置
    CACHE_TTL = int(os.getenv("CACHE_TTL", "300"))  # 5分钟
    
    # 页面配置
    PAGE_SIZE = 20
    CHART_HEIGHT = 400
    
    # 主题配置
    PRIMARY_COLOR = "#FF6B6B"
    BACKGROUND_COLOR = "#FFFFFF"
    SECONDARY_BACKGROUND_COLOR = "#F0F2F6"
    TEXT_COLOR = "#262730"
    
    @classmethod
    def get_file_type_display_name(cls, mime_type: str) -> str:
        """获取文件类型显示名称"""
        type_map = {
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "Excel文件 (.xlsx)",
            "application/vnd.ms-excel": "Excel文件 (.xls)",
            "text/csv": "CSV文件 (.csv)",
            "message/rfc822": "邮件文件 (.eml)",
            "application/vnd.ms-outlook": "Outlook邮件 (.msg)"
        }
        return type_map.get(mime_type, "未知文件类型")
    
    @classmethod
    def is_development(cls) -> bool:
        """是否为开发环境"""
        return cls.DEBUG
    
    @classmethod
    def is_production(cls) -> bool:
        """是否为生产环境"""
        return not cls.DEBUG


# 页面路由配置
PAGE_ROUTES = {
    "数据上传": "pages/01_数据上传.py",
    "生产规划": "pages/02_生产规划.py",
    "计划监控": "pages/03_计划监控.py",
    "数据分析": "pages/04_数据分析.py",
    "智能助手": "pages/05_智能助手.py",
    "用户管理": "pages/06_用户管理.py",
    "系统设置": "pages/07_系统设置.py"
}

# 权限配置
PERMISSION_PAGES = {
    "pages/06_用户管理.py": ["user.view", "user.create", "user.update"],
    "pages/07_系统设置.py": ["system.view", "system.config"]
}

# 图表配置
CHART_CONFIG = {
    "gantt": {
        "height": 600,
        "color_scheme": "Set3"
    },
    "bar": {
        "height": 400,
        "color_scheme": "Blues"
    },
    "line": {
        "height": 400,
        "color_scheme": "Viridis"
    },
    "scatter": {
        "height": 400,
        "size": 100
    }
}

# 消息配置
MESSAGES = {
    "login_success": "✅ 登录成功！",
    "login_failed": "❌ 用户名或密码错误",
    "logout_success": "✅ 已安全退出",
    "upload_success": "✅ 文件上传成功",
    "upload_failed": "❌ 文件上传失败",
    "save_success": "✅ 保存成功",
    "save_failed": "❌ 保存失败",
    "delete_success": "✅ 删除成功",
    "delete_failed": "❌ 删除失败",
    "permission_denied": "❌ 权限不足",
    "network_error": "❌ 网络连接错误",
    "server_error": "❌ 服务器错误"
}
