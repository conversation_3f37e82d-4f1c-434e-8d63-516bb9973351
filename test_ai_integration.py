#!/usr/bin/env python3
"""
AI能力增强功能集成测试脚本
验证所有AI功能是否正常工作
"""

import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试模块导入...")
    
    try:
        from services.ai_enhancement_service import (
            AIEnhancementService,
            PredictiveAnalyticsEngine,
            AnomalyDetectionEngine,
            IntelligentOptimizationEngine,
            PredictionType,
            AnomalyType,
            OptimizationType
        )
        print("✅ AI增强服务导入成功")
        
        from config.ai_enhancement_config import (
            PREDICTION_CONFIGS,
            ANOMALY_DETECTION_CONFIGS,
            OPTIMIZATION_CONFIGS,
            AI_SERVICE_CONFIG
        )
        print("✅ AI配置导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

async def test_predictive_analytics():
    """测试预测分析功能"""
    print("\n📈 测试预测分析功能...")
    
    try:
        from services.ai_enhancement_service import PredictiveAnalyticsEngine, PredictionType
        
        engine = PredictiveAnalyticsEngine()
        
        # 测试需求预测
        input_data = {
            "base_demand": 100,
            "historical_data": "30天历史数据"
        }
        
        result = await engine.predict_demand(input_data, forecast_horizon=7)
        
        assert result.prediction_type == PredictionType.DEMAND_FORECAST
        assert len(result.predictions) == 7
        assert all(pred > 0 for pred in result.predictions)
        
        print("✅ 需求预测功能正常")
        
        # 测试设备故障预测
        equipment_data = {
            "equipment_ids": ["L01", "L02", "L03", "L04"]
        }
        
        failure_result = await engine.predict_equipment_failure(equipment_data)
        
        assert failure_result.prediction_type == PredictionType.EQUIPMENT_FAILURE
        assert len(failure_result.predictions) == 4
        assert all(0 <= prob <= 1 for prob in failure_result.predictions)
        
        print("✅ 设备故障预测功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测分析测试失败: {e}")
        return False

async def test_anomaly_detection():
    """测试异常检测功能"""
    print("\n🔍 测试异常检测功能...")
    
    try:
        from services.ai_enhancement_service import AnomalyDetectionEngine, AnomalyType
        
        engine = AnomalyDetectionEngine()
        
        # 创建测试数据
        np.random.seed(42)
        data = []
        for i in range(50):
            value = np.random.normal(100, 10)
            # 注入一些异常
            if i in [10, 25, 40]:
                value = np.random.choice([150, 50])
            
            data.append({
                "timestamp": datetime.now() - timedelta(hours=50-i),
                "value": value,
                "equipment": f"L{(i%4)+1:02d}"
            })
        
        df = pd.DataFrame(data)
        
        # 设置检测器
        setup_result = await engine.setup_anomaly_detectors(df)
        assert setup_result["success"] is True
        print("✅ 异常检测器设置成功")
        
        # 执行异常检测
        result = await engine.detect_anomalies(df, AnomalyType.ENSEMBLE)
        
        assert isinstance(result.anomalies, list)
        assert 0 <= result.detection_rate <= 1
        
        print(f"✅ 异常检测功能正常，检测到 {len(result.anomalies)} 个异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常检测测试失败: {e}")
        return False

async def test_intelligent_optimization():
    """测试智能优化功能"""
    print("\n⚡ 测试智能优化功能...")
    
    try:
        from services.ai_enhancement_service import IntelligentOptimizationEngine, OptimizationType
        
        engine = IntelligentOptimizationEngine()
        
        # 测试生产排程优化
        production_data = {
            "orders": [
                {"order_id": "O001", "processing_time": 120, "priority": 1},
                {"order_id": "O002", "processing_time": 90, "priority": 2},
                {"order_id": "O003", "processing_time": 150, "priority": 1}
            ],
            "equipment": {
                "L01": {"status": "available", "efficiency": 0.9},
                "L02": {"status": "available", "efficiency": 0.85},
                "L03": {"status": "maintenance", "efficiency": 0.0}
            }
        }
        
        result = await engine.optimize(
            OptimizationType.PRODUCTION_SCHEDULE, 
            production_data
        )
        
        assert result.optimization_type == OptimizationType.PRODUCTION_SCHEDULE
        assert result.optimal_solution is not None
        assert "schedule" in result.optimal_solution
        assert result.improvement_percentage >= 0
        
        print("✅ 生产排程优化功能正常")
        
        # 测试能耗优化
        energy_data = {
            "equipment": {
                "L01": {"power_rating": 50},
                "L02": {"power_rating": 45}
            },
            "schedule": production_data["orders"]
        }
        
        energy_result = await engine.optimize(
            OptimizationType.ENERGY_OPTIMIZATION,
            energy_data
        )
        
        assert energy_result.optimization_type == OptimizationType.ENERGY_OPTIMIZATION
        assert "energy_plan" in energy_result.optimal_solution
        
        print("✅ 能耗优化功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能优化测试失败: {e}")
        return False

async def test_comprehensive_analysis():
    """测试综合AI分析功能"""
    print("\n📊 测试综合AI分析功能...")
    
    try:
        from services.ai_enhancement_service import AIEnhancementService
        
        service = AIEnhancementService()
        
        # 初始化服务
        init_result = await service.initialize_ai_services()
        assert init_result["success"] is True
        print("✅ AI服务初始化成功")
        
        # 准备测试数据
        input_data = {
            "orders": [
                {"order_id": "O001", "processing_time": 120},
                {"order_id": "O002", "processing_time": 90}
            ],
            "equipment": {
                "L01": {"status": "available", "efficiency": 0.9},
                "L02": {"status": "available", "efficiency": 0.85}
            },
            "monitoring_data": [
                {"timestamp": datetime.now(), "value": 100, "equipment": "L01"},
                {"timestamp": datetime.now(), "value": 95, "equipment": "L02"},
                {"timestamp": datetime.now(), "value": 150, "equipment": "L01"}  # 异常值
            ]
        }
        
        # 执行综合分析
        result = await service.get_comprehensive_ai_analysis(input_data)
        
        assert result["success"] is True
        assert "results" in result
        
        print("✅ 综合AI分析功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合AI分析测试失败: {e}")
        return False

def test_configuration():
    """测试配置文件"""
    print("\n🔧 测试配置文件...")
    
    try:
        from config.ai_enhancement_config import (
            get_prediction_config,
            get_anomaly_config,
            get_optimization_config,
            validate_performance
        )
        
        # 测试预测配置
        demand_config = get_prediction_config("demand_forecast")
        assert demand_config is not None
        assert demand_config.name == "需求预测"
        
        # 测试异常检测配置
        anomaly_config = get_anomaly_config("production_monitoring")
        assert anomaly_config is not None
        assert anomaly_config.name == "生产监控异常检测"
        
        # 测试优化配置
        optimization_config = get_optimization_config("production_scheduling")
        assert optimization_config is not None
        assert optimization_config.name == "生产排程优化"
        
        # 测试性能验证
        performance_level = validate_performance("prediction_accuracy", "demand_forecast", 0.92)
        assert performance_level in ["excellent", "good", "acceptable", "poor"]
        
        print("✅ 配置文件功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_frontend_page():
    """测试前端页面导入"""
    print("\n🖥️ 测试前端页面...")
    
    try:
        # 检查页面文件是否存在
        page_path = os.path.join("frontend", "pages", "12_AI能力增强.py")
        if not os.path.exists(page_path):
            print(f"❌ 页面文件不存在: {page_path}")
            return False
        
        # 检查页面内容
        with open(page_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键组件
        required_components = [
            "streamlit",
            "AI能力增强",
            "预测分析",
            "异常检测",
            "智能优化",
            "综合分析"
        ]
        
        for component in required_components:
            if component not in content:
                print(f"❌ 页面缺少组件: {component}")
                return False
        
        print("✅ 前端页面结构正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端页面测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始AI能力增强功能集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 基础测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("配置文件", test_configuration()))
    test_results.append(("前端页面", test_frontend_page()))
    
    # 功能测试
    test_results.append(("预测分析", await test_predictive_analytics()))
    test_results.append(("异常检测", await test_anomaly_detection()))
    test_results.append(("智能优化", await test_intelligent_optimization()))
    test_results.append(("综合分析", await test_comprehensive_analysis()))
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 总计: {passed + failed} 个测试")
    print(f"✅ 通过: {passed} 个")
    print(f"❌ 失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！AI能力增强功能集成正常。")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
