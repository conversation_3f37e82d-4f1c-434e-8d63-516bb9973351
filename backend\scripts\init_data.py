#!/usr/bin/env python3
"""
初始化基础数据
"""

import asyncio
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('/app')

from app.core.database import AsyncSessionLocal
from app.models.user import User, Role, Permission, role_permission_association, user_role_association
from app.core.security import password_manager
from sqlalchemy import select


async def init_permissions():
    """初始化权限数据"""
    permissions_data = [
        # 用户管理权限
        {"code": "user.view", "name": "查看用户", "module": "user", "description": "查看用户列表和详情"},
        {"code": "user.create", "name": "创建用户", "module": "user", "description": "创建新用户"},
        {"code": "user.update", "name": "更新用户", "module": "user", "description": "更新用户信息"},
        {"code": "user.delete", "name": "删除用户", "module": "user", "description": "删除用户"},
        
        # 角色权限管理
        {"code": "role.view", "name": "查看角色", "module": "role", "description": "查看角色列表和详情"},
        {"code": "role.create", "name": "创建角色", "module": "role", "description": "创建新角色"},
        {"code": "role.update", "name": "更新角色", "module": "role", "description": "更新角色信息"},
        {"code": "role.delete", "name": "删除角色", "module": "role", "description": "删除角色"},
        
        # 数据源管理
        {"code": "datasource.view", "name": "查看数据源", "module": "datasource", "description": "查看数据源配置"},
        {"code": "datasource.create", "name": "创建数据源", "module": "datasource", "description": "创建数据源配置"},
        {"code": "datasource.update", "name": "更新数据源", "module": "datasource", "description": "更新数据源配置"},
        {"code": "datasource.delete", "name": "删除数据源", "module": "datasource", "description": "删除数据源配置"},
        
        # 文件上传
        {"code": "file.upload", "name": "文件上传", "module": "file", "description": "上传文件"},
        {"code": "file.view", "name": "查看文件", "module": "file", "description": "查看上传的文件"},
        {"code": "file.delete", "name": "删除文件", "module": "file", "description": "删除上传的文件"},
        
        # 生产计划
        {"code": "plan.view", "name": "查看生产计划", "module": "plan", "description": "查看生产计划"},
        {"code": "plan.create", "name": "创建生产计划", "module": "plan", "description": "创建生产计划"},
        {"code": "plan.update", "name": "更新生产计划", "module": "plan", "description": "更新生产计划"},
        {"code": "plan.delete", "name": "删除生产计划", "module": "plan", "description": "删除生产计划"},
        {"code": "plan.execute", "name": "执行计划算法", "module": "plan", "description": "执行生产计划算法"},
        
        # 设备管理
        {"code": "equipment.view", "name": "查看设备", "module": "equipment", "description": "查看设备信息"},
        {"code": "equipment.create", "name": "创建设备", "module": "equipment", "description": "创建设备信息"},
        {"code": "equipment.update", "name": "更新设备", "module": "equipment", "description": "更新设备信息"},
        {"code": "equipment.delete", "name": "删除设备", "module": "equipment", "description": "删除设备信息"},
        
        # LLM聊天
        {"code": "llm.chat", "name": "LLM聊天", "module": "llm", "description": "使用LLM智能助手"},
        {"code": "llm.config", "name": "LLM配置", "module": "llm", "description": "配置LLM服务"},
        
        # 系统管理
        {"code": "system.view", "name": "查看系统信息", "module": "system", "description": "查看系统状态和信息"},
        {"code": "system.config", "name": "系统配置", "module": "system", "description": "修改系统配置"},
        {"code": "system.logs", "name": "查看日志", "module": "system", "description": "查看系统日志"},
    ]
    
    async with AsyncSessionLocal() as db:
        try:
            for perm_data in permissions_data:
                # 检查权限是否已存在
                stmt = select(Permission).where(Permission.code == perm_data["code"])
                result = await db.execute(stmt)
                existing_perm = result.scalar_one_or_none()
                
                if not existing_perm:
                    permission = Permission(**perm_data)
                    db.add(permission)
            
            await db.commit()
            print("✅ 权限数据初始化完成")
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 权限数据初始化失败: {str(e)}")
            raise


async def init_roles():
    """初始化角色数据"""
    roles_data = [
        {
            "code": "super_admin",
            "name": "超级管理员",
            "description": "系统超级管理员，拥有所有权限",
            "permissions": "all"  # 特殊标记，表示所有权限
        },
        {
            "code": "admin",
            "name": "管理员",
            "description": "系统管理员，拥有大部分管理权限",
            "permissions": [
                "user.view", "user.create", "user.update",
                "role.view", "role.create", "role.update",
                "datasource.view", "datasource.create", "datasource.update",
                "file.upload", "file.view", "file.delete",
                "plan.view", "plan.create", "plan.update", "plan.execute",
                "equipment.view", "equipment.create", "equipment.update",
                "llm.chat", "llm.config",
                "system.view", "system.config", "system.logs"
            ]
        },
        {
            "code": "planner",
            "name": "生产规划员",
            "description": "生产规划人员，负责生产计划制定",
            "permissions": [
                "file.upload", "file.view",
                "plan.view", "plan.create", "plan.update", "plan.execute",
                "equipment.view",
                "llm.chat"
            ]
        },
        {
            "code": "operator",
            "name": "操作员",
            "description": "系统操作员，只能查看和基本操作",
            "permissions": [
                "plan.view",
                "equipment.view",
                "llm.chat"
            ]
        },
        {
            "code": "viewer",
            "name": "查看者",
            "description": "只读用户，只能查看信息",
            "permissions": [
                "plan.view",
                "equipment.view"
            ]
        }
    ]
    
    async with AsyncSessionLocal() as db:
        try:
            # 获取所有权限
            stmt = select(Permission)
            result = await db.execute(stmt)
            all_permissions = result.scalars().all()
            permission_map = {perm.code: perm for perm in all_permissions}
            
            for role_data in roles_data:
                # 检查角色是否已存在
                stmt = select(Role).where(Role.code == role_data["code"])
                result = await db.execute(stmt)
                existing_role = result.scalar_one_or_none()
                
                if not existing_role:
                    role = Role(
                        code=role_data["code"],
                        name=role_data["name"],
                        description=role_data["description"]
                    )
                    db.add(role)
                    await db.flush()  # 获取role.id
                    
                    # 分配权限
                    if role_data["permissions"] == "all":
                        # 分配所有权限
                        role.permissions = all_permissions
                    else:
                        # 分配指定权限
                        role_permissions = []
                        for perm_code in role_data["permissions"]:
                            if perm_code in permission_map:
                                role_permissions.append(permission_map[perm_code])
                        role.permissions = role_permissions
            
            await db.commit()
            print("✅ 角色数据初始化完成")
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 角色数据初始化失败: {str(e)}")
            raise


async def init_admin_user():
    """初始化管理员用户"""
    async with AsyncSessionLocal() as db:
        try:
            # 检查是否已存在管理员用户
            stmt = select(User).where(User.username == "admin")
            result = await db.execute(stmt)
            existing_user = result.scalar_one_or_none()
            
            if not existing_user:
                # 获取超级管理员角色
                stmt = select(Role).where(Role.code == "super_admin")
                result = await db.execute(stmt)
                admin_role = result.scalar_one_or_none()
                
                if admin_role:
                    # 创建管理员用户
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        full_name="系统管理员",
                        password_hash=password_manager.get_password_hash("admin123456"),
                        is_active=True,
                        is_superuser=True,
                        department="IT部门",
                        position="系统管理员"
                    )
                    
                    db.add(admin_user)
                    await db.flush()  # 获取user.id
                    
                    # 分配角色
                    admin_user.roles = [admin_role]
                    
                    await db.commit()
                    print("✅ 管理员用户创建成功")
                    print("   用户名: admin")
                    print("   密码: admin123456")
                    print("   ⚠️  请在生产环境中修改默认密码")
                else:
                    print("❌ 未找到超级管理员角色")
            else:
                print("ℹ️  管理员用户已存在")
                
        except Exception as e:
            await db.rollback()
            print(f"❌ 管理员用户创建失败: {str(e)}")
            raise


async def main():
    """主函数"""
    print("🔄 开始初始化基础数据...")
    
    try:
        await init_permissions()
        await init_roles()
        await init_admin_user()
        
        print("✅ 基础数据初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 基础数据初始化失败: {str(e)}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
