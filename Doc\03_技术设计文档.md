# 智能工厂生产管理规划系统 - 技术设计文档

## 数据模型设计

### 数据库架构

#### 主数据库：MySQL 8.0+
- **用途**：存储业务数据、配置信息、用户数据
- **特性**：ACID事务、复杂查询、数据一致性
- **连接池**：最大连接数20，空闲超时30分钟（适配小规模用户）

#### 缓存数据库：Redis 7.0+
- **用途**：会话管理、计算结果缓存、实时数据
- **特性**：高性能、多数据结构、持久化
- **配置**：最大内存1GB，LRU淘汰策略（小规模部署优化）

### 核心数据表设计

#### 1. 文件上传与数据提取表

```sql
-- 文件上传记录表
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) NOT NULL,
    file_type ENUM('email', 'excel', 'csv') NOT NULL,
    file_size BIGINT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT NOT NULL,
    status ENUM('uploaded', 'processing', 'completed', 'failed') DEFAULT 'uploaded',
    error_message TEXT,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_upload_time (upload_time),
    INDEX idx_status (status)
);

-- 邮件内容提取表
CREATE TABLE email_extractions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_upload_id INT NOT NULL,
    email_subject VARCHAR(500),
    email_sender VARCHAR(255),
    email_date DATETIME,
    extracted_tables JSON, -- 存储提取的表格数据
    table_count INT DEFAULT 0,
    extraction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_upload_id) REFERENCES file_uploads(id),
    INDEX idx_extraction_time (extraction_time)
);

-- 数据源配置表（灵活配置）
CREATE TABLE data_source_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    source_name VARCHAR(100) NOT NULL,
    source_type ENUM('database', 'file', 'api', 'email') NOT NULL,
    config_data JSON NOT NULL, -- 存储连接配置、字段映射等
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_source_type (source_type),
    INDEX idx_active (is_active)
);

-- 约束条件配置表（灵活配置生产约束）
CREATE TABLE constraint_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    constraint_name VARCHAR(100) NOT NULL,
    constraint_type VARCHAR(50) NOT NULL, -- capacity, time, resource, custom
    constraint_data JSON NOT NULL, -- 约束参数和规则
    is_active BOOLEAN DEFAULT TRUE,
    priority INT DEFAULT 5, -- 1-10，数字越小优先级越高
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_constraint_type (constraint_type),
    INDEX idx_active_priority (is_active, priority)
);
```

#### 2. 设备管理表

```sql
-- 设备信息表
CREATE TABLE equipment (
    id INT PRIMARY KEY AUTO_INCREMENT,
    equipment_code VARCHAR(50) UNIQUE NOT NULL,
    equipment_name VARCHAR(100) NOT NULL,
    equipment_type ENUM('TANK', 'FINISHING') NOT NULL,
    location VARCHAR(100),
    status TINYINT DEFAULT 1, -- 0:停用 1:运行 2:故障 3:维护
    capacity DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type_status (equipment_type, status),
    INDEX idx_location (location)
);

-- 设备参数表
CREATE TABLE equipment_parameters (
    id INT PRIMARY KEY AUTO_INCREMENT,
    equipment_id INT NOT NULL,
    parameter_name VARCHAR(100) NOT NULL,
    parameter_value VARCHAR(255) NOT NULL,
    parameter_unit VARCHAR(50),
    effective_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    UNIQUE KEY uk_equipment_param_date (equipment_id, parameter_name, effective_date)
);
```

#### 2. 生产计划表

```sql
-- 生产计划主表
CREATE TABLE production_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plan_no VARCHAR(50) UNIQUE NOT NULL,
    year INT NOT NULL,
    month TINYINT NOT NULL,
    status ENUM('draft', 'confirmed', 'executing', 'completed', 'cancelled') DEFAULT 'draft',
    planner_id INT NOT NULL,
    algorithm_config JSON,
    optimization_result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_year_month (year, month),
    INDEX idx_status (status),
    FOREIGN KEY (planner_id) REFERENCES users(id)
);

-- 生产计划明细表
CREATE TABLE production_plan_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plan_id INT NOT NULL,
    equipment_id INT NOT NULL,
    product_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    planned_quantity DECIMAL(10,2) NOT NULL,
    priority TINYINT DEFAULT 3,
    status ENUM('pending', 'executing', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES production_plans(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_plan_equipment (plan_id, equipment_id),
    INDEX idx_date_range (start_date, end_date)
);
```

#### 3. 学习与优化表

```sql
-- 算法配置表
CREATE TABLE algorithm_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    algorithm_name VARCHAR(100) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    parameters JSON NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    performance_metrics JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 学习反馈表
CREATE TABLE learning_feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plan_id INT NOT NULL,
    feedback_type ENUM('execution_result', 'user_adjustment', 'performance_metric') NOT NULL,
    feedback_data JSON NOT NULL,
    feedback_score DECIMAL(3,2), -- 0.00-1.00
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES production_plans(id),
    INDEX idx_plan_type (plan_id, feedback_type),
    INDEX idx_created_at (created_at)
);
```

### Redis缓存设计

#### 缓存键命名规范
```
模块:子模块:标识符[:附加信息]
```

#### 主要缓存类型

```python
# 设备状态缓存 (Hash, TTL: 5分钟)
equipment:status:{equipment_id} = {
    "status": 1,
    "last_update": "2024-01-15T10:30:00Z",
    "current_task": "task_123",
    "utilization": 0.85
}

# 计算结果缓存 (String, TTL: 1小时)
calculation:oee:{equipment_id}:{year}:{month} = {
    "oee_value": 0.82,
    "calculated_at": "2024-01-15T10:00:00Z",
    "factors": {"availability": 0.9, "performance": 0.95, "quality": 0.96}
}

# 用户会话 (Hash, TTL: 2小时)
session:{session_id} = {
    "user_id": 123,
    "login_time": "2024-01-15T08:00:00Z",
    "permissions": ["plan.read", "plan.create"],
    "last_activity": "2024-01-15T10:30:00Z"
}

# 任务队列 (List)
queue:calculation = ["task_1", "task_2", "task_3"]
queue:notification = ["notify_1", "notify_2"]
```

## API设计规范

### RESTful API设计原则

#### 1. URL设计规范
```
# 资源集合
GET    /api/v1/production-plans          # 获取计划列表
POST   /api/v1/production-plans          # 创建新计划

# 特定资源
GET    /api/v1/production-plans/{id}     # 获取特定计划
PUT    /api/v1/production-plans/{id}     # 更新特定计划
DELETE /api/v1/production-plans/{id}     # 删除特定计划

# 子资源
GET    /api/v1/production-plans/{id}/details    # 获取计划明细
POST   /api/v1/production-plans/{id}/execute    # 执行计划

# 操作动作
POST   /api/v1/production-plans/generate        # 生成计划
POST   /api/v1/algorithms/optimize              # 执行优化
```

#### 2. 统一响应格式
```json
{
  "success": true,
  "data": {
    // 实际数据内容
  },
  "message": "操作成功",
  "error_code": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789",
  "pagination": {  // 分页信息（如适用）
    "page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

#### 3. 错误处理规范
```json
{
  "success": false,
  "data": null,
  "message": "参数验证失败",
  "error_code": "VALIDATION_ERROR",
  "errors": [
    {
      "field": "start_date",
      "message": "开始日期不能为空",
      "code": "REQUIRED_FIELD"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 核心API接口

#### 1. 文件上传与数据提取API

```python
# 文件上传接口
POST /api/v1/files/upload
Content-Type: multipart/form-data

{
  "file": "binary_file_data",
  "file_type": "email|excel|csv",
  "description": "文件描述"
}

# 响应
{
  "success": true,
  "data": {
    "file_id": "file_123",
    "file_name": "orders_2024.xlsx",
    "file_type": "excel",
    "status": "uploaded",
    "upload_time": "2024-01-15T10:30:00Z"
  }
}

# 邮件内容提取接口
POST /api/v1/files/{file_id}/extract-email
{
  "extract_tables": true,
  "table_detection_method": "auto|manual",
  "target_data_types": ["orders", "inventory", "equipment"]
}

# 响应
{
  "success": true,
  "data": {
    "extraction_id": "ext_456",
    "email_subject": "2024年2月生产订单",
    "email_sender": "<EMAIL>",
    "email_date": "2024-01-15T08:00:00Z",
    "extracted_tables": [
      {
        "table_index": 0,
        "table_type": "orders",
        "row_count": 25,
        "columns": ["订单号", "产品", "数量", "交期"],
        "data_preview": [
          ["ORD001", "产品A", 100, "2024-02-15"],
          ["ORD002", "产品B", 50, "2024-02-20"]
        ]
      }
    ],
    "processing_time": 2.3
  }
}

# Excel数据解析接口
POST /api/v1/files/{file_id}/parse-excel
{
  "sheet_name": "订单数据",
  "header_row": 1,
  "data_start_row": 2,
  "column_mapping": {
    "A": "order_id",
    "B": "product_name",
    "C": "quantity",
    "D": "due_date"
  }
}

# 数据源配置接口
POST /api/v1/data-sources/config
{
  "source_name": "邮件订单数据源",
  "source_type": "email",
  "config_data": {
    "email_pattern": "订单.*xlsx",
    "auto_extract": true,
    "table_mapping": {
      "orders": {
        "required_columns": ["订单号", "产品", "数量"],
        "date_columns": ["交期"],
        "numeric_columns": ["数量"]
      }
    }
  }
}

# 约束条件配置接口
POST /api/v1/constraints/config
{
  "constraint_name": "设备产能限制",
  "constraint_type": "capacity",
  "constraint_data": {
    "equipment_ids": ["EQ001", "EQ002"],
    "max_utilization": 0.9,
    "time_windows": [
      {"start": "08:00", "end": "18:00", "days": ["Mon", "Tue", "Wed", "Thu", "Fri"]}
    ]
  },
  "priority": 1
}
```

#### 2. 本地LLM集成API

```python
# Ollama本地LLM查询接口
POST /api/v1/llm/ollama/query
{
  "model": "llama2|mistral|codellama",
  "prompt": "分析当前生产计划的瓶颈问题",
  "context": {
    "equipment_data": {...},
    "current_plan": {...},
    "historical_performance": {...}
  },
  "parameters": {
    "temperature": 0.3,
    "max_tokens": 1000
  }
}

# Azure OpenAI查询接口
POST /api/v1/llm/azure/query
{
  "deployment_name": "gpt-4",
  "messages": [
    {
      "role": "system",
      "content": "你是一个生产规划专家..."
    },
    {
      "role": "user",
      "content": "请分析以下生产数据并提供优化建议..."
    }
  ],
  "parameters": {
    "temperature": 0.3,
    "max_tokens": 1500
  }
}

# 统一LLM响应格式
{
  "success": true,
  "data": {
    "response": "根据分析，当前生产计划存在以下瓶颈...",
    "confidence_score": 0.85,
    "processing_time": 3.2,
    "model_used": "ollama:llama2",
    "suggestions": [
      {
        "type": "equipment_optimization",
        "description": "建议增加EQ001的维护频率",
        "impact": "可提升5%的设备利用率"
      }
    ]
  }
}
```

#### 3. 生产计划API

```python
# 生成生产计划
POST /api/v1/production-plans/generate
{
  "year": 2024,
  "month": 2,
  "planning_horizon_days": 30,
  "optimization_objectives": {
    "minimize_makespan": 0.4,
    "maximize_utilization": 0.3,
    "minimize_changeover": 0.3
  },
  "constraints": [
    {
      "type": "capacity_limit",
      "equipment_ids": ["eq_001", "eq_002"],
      "max_utilization": 0.9
    }
  ],
  "algorithm_config": {
    "algorithm_type": "milp",
    "max_solve_time": 1800,
    "gap_tolerance": 0.01
  }
}

# 响应
{
  "success": true,
  "data": {
    "plan_id": "plan_202402_001",
    "status": "draft",
    "generation_time": 1245,  // 秒
    "objective_value": 0.87,
    "statistics": {
      "total_orders": 45,
      "total_products": 12,
      "average_utilization": 0.84,
      "makespan_days": 28
    },
    "warnings": [
      "设备EQ_003在第15天利用率达到95%，接近上限"
    ]
  }
}
```

#### 2. LLM交互API

```python
# LLM查询分析
POST /api/v1/llm/analyze
{
  "query": "分析上个月Tank设备的效率问题",
  "context": {
    "time_range": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "equipment_types": ["TANK"],
    "include_historical_comparison": true
  },
  "parameters": {
    "max_tokens": 1000,
    "temperature": 0.3,
    "include_data_references": true
  }
}

# 响应
{
  "success": true,
  "data": {
    "analysis": "根据1月份数据分析，Tank设备整体效率为78%，低于目标85%。主要问题包括：\n\n1. 设备EQ_001的可用率仅为82%，主要由于计划外停机...",
    "confidence_score": 0.89,
    "data_references": [
      {
        "source": "equipment_oee",
        "equipment_id": "EQ_001",
        "metric": "availability",
        "value": 0.82,
        "period": "2024-01"
      }
    ],
    "recommendations": [
      {
        "priority": "high",
        "action": "增加EQ_001的预防性维护频率",
        "expected_impact": "可用率提升至90%以上"
      }
    ],
    "processing_time": 2.3
  }
}
```

### API安全与认证

#### 1. JWT认证流程
```python
# 登录获取令牌
POST /api/v1/auth/login
{
  "username": "planner001",
  "password": "secure_password",
  "remember_me": false
}

# 响应
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "def50200a1b2c3d4e5f6...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user_info": {
      "id": 123,
      "username": "planner001",
      "role": "planner",
      "permissions": ["plan.read", "plan.create", "plan.update"]
    }
  }
}

# 使用令牌访问API
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 2. 权限控制
```python
# 权限装饰器示例
@require_permissions(["plan.create"])
@rate_limit(requests=10, window=60)  # 每分钟最多10次请求
async def create_production_plan(request: Request):
    # 实现逻辑
    pass
```

## 模块设计详解

### 1. 数据源集成模块

#### 适配器模式实现
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any
import pandas as pd

class DataSourceAdapter(ABC):
    """数据源适配器基类"""

    @abstractmethod
    async def connect(self, config: Dict[str, Any]) -> bool:
        """建立连接"""
        pass

    @abstractmethod
    async def extract_data(self, query: Dict[str, Any]) -> pd.DataFrame:
        """提取数据"""
        pass

    @abstractmethod
    async def validate_data(self, data: pd.DataFrame) -> List[str]:
        """验证数据，返回错误列表"""
        pass

    @abstractmethod
    async def transform_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """转换数据格式"""
        pass

class SQLDataSourceAdapter(DataSourceAdapter):
    """SQL数据源适配器"""

    def __init__(self):
        self.connection = None

    async def connect(self, config: Dict[str, Any]) -> bool:
        # 实现SQL连接逻辑
        pass

    async def extract_data(self, query: Dict[str, Any]) -> pd.DataFrame:
        # 执行SQL查询
        pass

class ExcelDataSourceAdapter(DataSourceAdapter):
    """Excel数据源适配器"""

    async def extract_data(self, query: Dict[str, Any]) -> pd.DataFrame:
        file_path = query.get("file_path")
        sheet_name = query.get("sheet_name", 0)
        return pd.read_excel(file_path, sheet_name=sheet_name)
```

### 2. 算法模块设计

#### 算法注册与管理
```python
from typing import Protocol, Dict, Any, List
from dataclasses import dataclass

@dataclass
class OptimizationResult:
    """优化结果"""
    objective_value: float
    solution: Dict[str, Any]
    solve_time: float
    status: str
    gap: float = 0.0

class OptimizationAlgorithm(Protocol):
    """优化算法协议"""

    def solve(self,
             problem_data: Dict[str, Any],
             constraints: List[Dict[str, Any]],
             objectives: Dict[str, float]) -> OptimizationResult:
        """求解优化问题"""
        ...

class AlgorithmRegistry:
    """算法注册中心"""

    def __init__(self):
        self._algorithms: Dict[str, OptimizationAlgorithm] = {}

    def register(self, name: str, algorithm: OptimizationAlgorithm):
        """注册算法"""
        self._algorithms[name] = algorithm

    def get_algorithm(self, name: str) -> OptimizationAlgorithm:
        """获取算法实例"""
        if name not in self._algorithms:
            raise ValueError(f"Algorithm {name} not found")
        return self._algorithms[name]

    def list_algorithms(self) -> List[str]:
        """列出所有可用算法"""
        return list(self._algorithms.keys())

# 全局算法注册中心
algorithm_registry = AlgorithmRegistry()
```

### 3. 插件系统设计

#### 插件接口定义
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List

class PluginContext:
    """插件上下文"""

    def __init__(self, app_instance, config: Dict[str, Any]):
        self.app = app_instance
        self.config = config
        self.logger = self._create_logger()

    def get_service(self, service_name: str):
        """获取系统服务"""
        return self.app.get_service(service_name)

    def get_config(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)

class Plugin(ABC):
    """插件基类"""

    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取插件元数据"""
        pass

    @abstractmethod
    async def initialize(self, context: PluginContext) -> bool:
        """初始化插件"""
        pass

    @abstractmethod
    async def start(self) -> bool:
        """启动插件"""
        pass

    @abstractmethod
    async def stop(self) -> bool:
        """停止插件"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass

class PluginManager:
    """插件管理器"""

    def __init__(self):
        self.plugins: Dict[str, Plugin] = {}
        self.plugin_states: Dict[str, str] = {}

    async def load_plugin(self, plugin_path: str) -> bool:
        """加载插件"""
        # 实现插件加载逻辑
        pass

    async def enable_plugin(self, plugin_id: str) -> bool:
        """启用插件"""
        if plugin_id in self.plugins:
            success = await self.plugins[plugin_id].start()
            if success:
                self.plugin_states[plugin_id] = "enabled"
            return success
        return False
```

### 4. 缓存策略设计

#### 多层缓存架构
```python
from typing import Optional, Any, Union
import json
import redis
from datetime import timedelta

class CacheManager:
    """缓存管理器"""

    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.local_cache = {}  # 本地缓存

    async def get(self, key: str, use_local: bool = True) -> Optional[Any]:
        """获取缓存值"""
        # 先查本地缓存
        if use_local and key in self.local_cache:
            return self.local_cache[key]

        # 查Redis缓存
        value = await self.redis.get(key)
        if value:
            parsed_value = json.loads(value)
            if use_local:
                self.local_cache[key] = parsed_value
            return parsed_value

        return None

    async def set(self,
                  key: str,
                  value: Any,
                  ttl: Union[int, timedelta] = None,
                  use_local: bool = True) -> bool:
        """设置缓存值"""
        serialized_value = json.dumps(value, default=str)

        # 设置Redis缓存
        if ttl:
            await self.redis.setex(key, ttl, serialized_value)
        else:
            await self.redis.set(key, serialized_value)

        # 设置本地缓存
        if use_local:
            self.local_cache[key] = value

        return True

    async def delete(self, key: str) -> bool:
        """删除缓存"""
        # 删除本地缓存
        self.local_cache.pop(key, None)

        # 删除Redis缓存
        return await self.redis.delete(key) > 0

    def clear_local_cache(self):
        """清空本地缓存"""
        self.local_cache.clear()
```

## 性能优化策略

### 1. 数据库优化

#### 索引策略
```sql
-- 复合索引优化查询
CREATE INDEX idx_plan_equipment_date ON production_plan_details
(plan_id, equipment_id, start_date, end_date);

-- 覆盖索引减少回表
CREATE INDEX idx_equipment_status_cover ON equipment
(equipment_type, status) INCLUDE (equipment_name, capacity);

-- 分区表优化大数据量查询
CREATE TABLE learning_feedback_2024 PARTITION OF learning_feedback
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

#### 查询优化
```python
# 使用批量操作减少数据库往返
async def batch_update_plan_details(details: List[PlanDetail]):
    """批量更新计划明细"""
    values = [(d.planned_quantity, d.priority, d.id) for d in details]
    query = """
        UPDATE production_plan_details
        SET planned_quantity = %s, priority = %s
        WHERE id = %s
    """
    await database.executemany(query, values)

# 使用连接查询减少N+1问题
async def get_plans_with_details(plan_ids: List[int]):
    """获取计划及其明细"""
    query = """
        SELECT p.*, pd.*
        FROM production_plans p
        LEFT JOIN production_plan_details pd ON p.id = pd.plan_id
        WHERE p.id IN %s
    """
    return await database.fetch_all(query, (tuple(plan_ids),))
```

### 2. 缓存优化

#### 缓存预热策略
```python
class CacheWarmer:
    """缓存预热器"""

    async def warm_equipment_cache(self):
        """预热设备缓存"""
        equipment_list = await self.db.get_active_equipment()
        for equipment in equipment_list:
            cache_key = f"equipment:status:{equipment.id}"
            await self.cache.set(cache_key, equipment.to_dict(), ttl=300)

    async def warm_calculation_cache(self):
        """预热计算结果缓存"""
        current_month = datetime.now().strftime("%Y-%m")
        for equipment_id in await self.db.get_equipment_ids():
            oee_data = await self.calculate_oee(equipment_id, current_month)
            cache_key = f"calculation:oee:{equipment_id}:{current_month}"
            await self.cache.set(cache_key, oee_data, ttl=3600)
```

### 3. 异步处理

#### 后台任务队列
```python
from celery import Celery
from typing import Dict, Any

# Celery配置
celery_app = Celery(
    'smart_aps',
    broker='redis://localhost:6379/1',
    backend='redis://localhost:6379/2'
)

@celery_app.task(bind=True, max_retries=3)
def generate_production_plan_task(self, plan_config: Dict[str, Any]):
    """异步生成生产计划"""
    try:
        # 执行计划生成逻辑
        result = planning_engine.generate_plan(plan_config)
        return {
            "status": "success",
            "plan_id": result.plan_id,
            "generation_time": result.generation_time
        }
    except Exception as exc:
        # 重试机制
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=exc)
        return {
            "status": "failed",
            "error": str(exc)
        }

@celery_app.task
def send_notification_task(recipients: List[str], message: Dict[str, Any]):
    """异步发送通知"""
    notification_service.send_bulk_notification(recipients, message)
```

## 安全设计

### 1. 认证与授权

#### JWT令牌管理
```python
import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional

class JWTManager:
    """JWT令牌管理器"""

    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire = timedelta(hours=1)
        self.refresh_token_expire = timedelta(days=7)

    def create_access_token(self, user_data: Dict) -> str:
        """创建访问令牌"""
        payload = {
            "user_id": user_data["id"],
            "username": user_data["username"],
            "role": user_data["role"],
            "permissions": user_data["permissions"],
            "exp": datetime.utcnow() + self.access_token_expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

# 权限检查装饰器
def require_permissions(required_permissions: List[str]):
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            token = request.headers.get("Authorization", "").replace("Bearer ", "")
            payload = jwt_manager.verify_token(token)

            if not payload:
                raise HTTPException(401, "Invalid or expired token")

            user_permissions = payload.get("permissions", [])
            if not all(perm in user_permissions for perm in required_permissions):
                raise HTTPException(403, "Insufficient permissions")

            request.state.user = payload
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
```

### 2. 数据安全

#### 敏感数据加密
```python
from cryptography.fernet import Fernet
import base64
import os

class DataEncryption:
    """数据加密工具"""

    def __init__(self):
        # 从环境变量获取密钥
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            key = Fernet.generate_key()
            print(f"Generated new encryption key: {key.decode()}")
        else:
            key = key.encode()
        self.cipher = Fernet(key)

    def encrypt(self, data: str) -> str:
        """加密数据"""
        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted_data = self.cipher.decrypt(encrypted_bytes)
        return decrypted_data.decode()

# 在模型中使用加密
class User(BaseModel):
    username: str
    email: str
    _password: str  # 私有字段

    def set_password(self, password: str):
        """设置加密密码"""
        self._password = encryption.encrypt(password)

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        try:
            decrypted = encryption.decrypt(self._password)
            return password == decrypted
        except:
            return False
```

### 3. API安全

#### 请求限流
```python
from collections import defaultdict
from datetime import datetime, timedelta
import asyncio

class RateLimiter:
    """API限流器"""

    def __init__(self):
        self.requests = defaultdict(list)
        self.limits = {
            "default": (100, 60),  # 100次/分钟
            "plan_generation": (10, 3600),  # 10次/小时
            "llm_query": (50, 3600),  # 50次/小时
        }

    async def is_allowed(self,
                        client_id: str,
                        endpoint: str = "default") -> bool:
        """检查是否允许请求"""
        now = datetime.utcnow()
        limit, window = self.limits.get(endpoint, self.limits["default"])

        # 清理过期记录
        cutoff = now - timedelta(seconds=window)
        self.requests[client_id] = [
            req_time for req_time in self.requests[client_id]
            if req_time > cutoff
        ]

        # 检查限制
        if len(self.requests[client_id]) >= limit:
            return False

        # 记录请求
        self.requests[client_id].append(now)
        return True

# 限流中间件
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    client_ip = request.client.host
    endpoint = request.url.path

    # 确定限流类型
    rate_limit_type = "default"
    if "/generate" in endpoint:
        rate_limit_type = "plan_generation"
    elif "/llm/" in endpoint:
        rate_limit_type = "llm_query"

    if not await rate_limiter.is_allowed(client_ip, rate_limit_type):
        return JSONResponse(
            status_code=429,
            content={"error": "Rate limit exceeded"}
        )

    response = await call_next(request)
    return response
```

## 监控与日志

### 1. 应用监控

#### 性能指标收集
```python
import time
from functools import wraps
from typing import Dict, Any
import psutil

class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self.metrics = defaultdict(list)

    def record_api_call(self, endpoint: str, duration: float, status_code: int):
        """记录API调用指标"""
        self.metrics["api_calls"].append({
            "endpoint": endpoint,
            "duration": duration,
            "status_code": status_code,
            "timestamp": datetime.utcnow()
        })

    def record_algorithm_performance(self, algorithm: str, solve_time: float,
                                   objective_value: float):
        """记录算法性能指标"""
        self.metrics["algorithm_performance"].append({
            "algorithm": algorithm,
            "solve_time": solve_time,
            "objective_value": objective_value,
            "timestamp": datetime.utcnow()
        })

    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "active_connections": len(psutil.net_connections()),
            "timestamp": datetime.utcnow()
        }

# 性能监控装饰器
def monitor_performance(metric_name: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                metrics_collector.record_metric(metric_name, duration, "success")
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_metric(metric_name, duration, "error")
                raise
        return wrapper
    return decorator
```

### 2. 结构化日志

#### 日志配置
```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    """结构化日志记录器"""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 文件处理器
        file_handler = logging.FileHandler('app.log')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def log_api_request(self, request_id: str, endpoint: str,
                       user_id: int, params: Dict[str, Any]):
        """记录API请求"""
        log_data = {
            "event_type": "api_request",
            "request_id": request_id,
            "endpoint": endpoint,
            "user_id": user_id,
            "params": params,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.logger.info(json.dumps(log_data))

    def log_algorithm_execution(self, algorithm: str, input_data: Dict[str, Any],
                              result: Dict[str, Any], execution_time: float):
        """记录算法执行"""
        log_data = {
            "event_type": "algorithm_execution",
            "algorithm": algorithm,
            "input_size": len(str(input_data)),
            "execution_time": execution_time,
            "result_quality": result.get("objective_value"),
            "timestamp": datetime.utcnow().isoformat()
        }
        self.logger.info(json.dumps(log_data))

    def log_error(self, error: Exception, context: Dict[str, Any]):
        """记录错误"""
        log_data = {
            "event_type": "error",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.logger.error(json.dumps(log_data))
```

这个技术设计文档现在包含了完整的技术架构、性能优化、安全设计和监控方案。
