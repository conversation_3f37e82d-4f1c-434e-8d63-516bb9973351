"""
增强的LLM服务
支持多轮对话、数据集成和智能生产计划生成
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import uuid

from .data_integration_service import data_integration_service


class ConversationManager:
    """对话管理器"""

    def __init__(self):
        self.db_path = "conversations.db"
        self._init_database()

    def _init_database(self):
        """初始化对话数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 对话会话表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                title TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                metadata TEXT
            )
        ''')

        # 对话消息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                conversation_id TEXT,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                data_context TEXT,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id)
            )
        ''')

        # 生产计划生成记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS plan_generations (
                id TEXT PRIMARY KEY,
                conversation_id TEXT,
                message_id TEXT,
                plan_data TEXT,
                generation_type TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id),
                FOREIGN KEY (message_id) REFERENCES messages (id)
            )
        ''')

        conn.commit()
        conn.close()

    def create_conversation(self, user_id: str, title: str = None) -> str:
        """创建新对话"""
        conversation_id = str(uuid.uuid4())

        if not title:
            title = f"对话 {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO conversations (id, user_id, title, metadata)
            VALUES (?, ?, ?, ?)
        ''', (
            conversation_id,
            user_id,
            title,
            json.dumps({"created_by": "llm_service"})
        ))

        conn.commit()
        conn.close()

        return conversation_id

    def add_message(self, conversation_id: str, role: str, content: str,
                   metadata: Dict[str, Any] = None, data_context: Dict[str, Any] = None) -> str:
        """添加消息到对话"""
        message_id = str(uuid.uuid4())

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO messages (id, conversation_id, role, content, metadata, data_context)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            message_id,
            conversation_id,
            role,
            content,
            json.dumps(metadata) if metadata else None,
            json.dumps(data_context) if data_context else None
        ))

        # 更新对话的最后更新时间
        cursor.execute('''
            UPDATE conversations
            SET updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (conversation_id,))

        conn.commit()
        conn.close()

        return message_id

    def get_conversation_history(self, conversation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取对话历史"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, role, content, timestamp, metadata, data_context
            FROM messages
            WHERE conversation_id = ?
            ORDER BY timestamp ASC
            LIMIT ?
        ''', (conversation_id, limit))

        messages = []
        for row in cursor.fetchall():
            message = {
                "id": row[0],
                "role": row[1],
                "content": row[2],
                "timestamp": row[3],
                "metadata": json.loads(row[4]) if row[4] else {},
                "data_context": json.loads(row[5]) if row[5] else {}
            }
            messages.append(message)

        conn.close()
        return messages

    def get_user_conversations(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取用户的对话列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, title, created_at, updated_at, status
            FROM conversations
            WHERE user_id = ?
            ORDER BY updated_at DESC
            LIMIT ?
        ''', (user_id, limit))

        conversations = []
        for row in cursor.fetchall():
            conversations.append({
                "id": row[0],
                "title": row[1],
                "created_at": row[2],
                "updated_at": row[3],
                "status": row[4]
            })

        conn.close()
        return conversations


class EnhancedLLMService:
    """增强的LLM服务 - 支持中英文对话"""

    def __init__(self):
        self.conversation_manager = ConversationManager()
        self.data_integration = data_integration_service
        self.supported_languages = ["zh", "en"]
        self.default_language = "zh"

    def detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的语言检测逻辑
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(text.replace(' ', ''))

        if total_chars == 0:
            return self.default_language

        chinese_ratio = chinese_chars / total_chars

        # 如果中文字符占比超过30%，认为是中文
        if chinese_ratio > 0.3:
            return "zh"
        else:
            return "en"

    def chat_with_context(self, message: str, conversation_id: str, user_id: str,
                         model: str = "gpt-3.5-turbo", temperature: float = 0.7,
                         include_data_context: bool = True, language: str = None) -> Dict[str, Any]:
        """带上下文的对话"""
        try:
            # 检测语言
            if language is None:
                language = self.detect_language(message)

            # 获取数据上下文
            data_context = None
            if include_data_context:
                data_context = self.data_integration.get_comprehensive_data_context()

            # 获取对话历史
            conversation_history = self.conversation_manager.get_conversation_history(conversation_id)

            # 记录用户消息
            user_message_id = self.conversation_manager.add_message(
                conversation_id=conversation_id,
                role="user",
                content=message,
                data_context=data_context
            )

            # 构建LLM提示
            llm_prompt = self._build_llm_prompt(message, conversation_history, data_context, language)

            # 调用LLM（这里使用模拟响应）
            llm_response = self._call_llm(llm_prompt, model, temperature, language)

            # 分析响应是否需要生成生产计划
            plan_generation = self._analyze_plan_generation_need(message, llm_response, language)

            # 记录助手消息
            assistant_message_id = self.conversation_manager.add_message(
                conversation_id=conversation_id,
                role="assistant",
                content=llm_response["content"],
                metadata={
                    "model": model,
                    "temperature": temperature,
                    "tokens_used": llm_response.get("tokens_used", 0),
                    "plan_generation": plan_generation
                }
            )

            # 如果需要生成生产计划，执行生成
            if plan_generation["needed"]:
                plan_result = self._generate_production_plan(
                    conversation_id, assistant_message_id, plan_generation, data_context
                )
                llm_response["plan_generation"] = plan_result

            # 记录用户输入到数据集成服务
            self.data_integration.record_user_input(
                input_type="llm_conversation",
                input_data={
                    "message": message,
                    "conversation_id": conversation_id,
                    "response": llm_response["content"],
                    "plan_generation": plan_generation
                },
                source_page="06_智能助手",
                user_id=user_id
            )

            return {
                "success": True,
                "data": {
                    "response": llm_response["content"],
                    "message_id": assistant_message_id,
                    "metadata": {
                        "model": model,
                        "tokens_used": llm_response.get("tokens_used", 0),
                        "data_context_included": include_data_context,
                        "plan_generation": plan_generation
                    }
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"对话处理失败: {str(e)}"
            }

    def _build_llm_prompt(self, message: str, conversation_history: List[Dict],
                         data_context: Dict[str, Any] = None, language: str = "zh") -> str:
        """构建LLM提示"""
        if language == "en":
            prompt = """You are a professional AI assistant for the Smart APS production management system. Your task is to help users optimize production management and provide professional analysis and recommendations.

System Capabilities:
- Production planning optimization
- Equipment status monitoring
- Quality management analysis
- Inventory optimization recommendations
- Data-driven decision making

"""
        else:
            prompt = """你是Smart APS生产管理系统的专业AI助手。你的任务是帮助用户优化生产管理，提供专业的分析和建议。

系统能力：
- 生产计划优化
- 设备状态监控
- 质量管理分析
- 库存优化建议
- 数据驱动决策

"""

        # 添加数据上下文
        if data_context:
            llm_context = self.data_integration.get_llm_prompt_context()
            if language == "en":
                prompt += f"\nCurrent System Data Status:\n{llm_context}\n"
            else:
                prompt += f"\n当前系统数据状态：\n{llm_context}\n"

        # 添加对话历史
        if conversation_history:
            if language == "en":
                prompt += "\nConversation History:\n"
                for msg in conversation_history[-5:]:  # 只包含最近5条消息
                    role_name = "User" if msg["role"] == "user" else "Assistant"
                    prompt += f"{role_name}: {msg['content']}\n"
            else:
                prompt += "\n对话历史：\n"
                for msg in conversation_history[-5:]:  # 只包含最近5条消息
                    role_name = "用户" if msg["role"] == "user" else "助手"
                    prompt += f"{role_name}: {msg['content']}\n"

        if language == "en":
            prompt += f"\nUser's Current Question: {message}\n"
            prompt += """
Please provide professional answers based on the above information. If the user's question involves production plan adjustments, equipment configuration changes, or data updates, please clearly state the specific operations that need to be performed in your response.

Response Requirements:
1. Professional and accurate, based on actual data
2. Provide specific and actionable recommendations
3. If a new production plan needs to be generated, please clearly state it
4. If charts or data need to be updated, please specify the specific content
"""
        else:
            prompt += f"\n用户当前问题: {message}\n"
            prompt += """
请基于以上信息提供专业的回答。如果用户的问题涉及生产计划调整、设备配置变更或数据更新，请在回答中明确说明需要执行的具体操作。

回答要求：
1. 专业准确，基于实际数据
2. 提供具体可行的建议
3. 如需生成新的生产计划，请明确说明
4. 如需更新图表或数据，请指出具体内容
"""

        return prompt

    def _call_llm(self, prompt: str, model: str, temperature: float, language: str = "zh") -> Dict[str, Any]:
        """调用LLM（模拟实现）"""
        # 这里应该调用实际的LLM API（Azure OpenAI、Ollama等）
        # 现在使用智能模拟响应

        response_content = self._generate_intelligent_response(prompt, language)

        return {
            "content": response_content,
            "tokens_used": len(response_content),
            "model": model,
            "temperature": temperature,
            "language": language
        }

    def _generate_intelligent_response(self, prompt: str, language: str = "zh") -> str:
        """生成智能响应"""
        # 分析用户问题类型
        if language == "en":
            user_message = prompt.split("User's Current Question: ")[-1].split("\n")[0]

            if any(keyword in user_message.lower() for keyword in ["production plan", "schedule", "planning", "optimize plan"]):
                return self._generate_production_planning_response(user_message, prompt, language)
            elif any(keyword in user_message.lower() for keyword in ["equipment", "machine", "maintenance", "status"]):
                return self._generate_equipment_response(user_message, prompt, language)
            elif any(keyword in user_message.lower() for keyword in ["quality", "defect", "inspection", "testing"]):
                return self._generate_quality_response(user_message, prompt, language)
            elif any(keyword in user_message.lower() for keyword in ["inventory", "material", "stock", "fifo"]):
                return self._generate_inventory_response(user_message, prompt, language)
            else:
                return self._generate_general_response(user_message, prompt, language)
        else:
            user_message = prompt.split("用户当前问题: ")[-1].split("\n")[0]

            if any(keyword in user_message for keyword in ["生产计划", "排程", "调整计划", "优化计划"]):
                return self._generate_production_planning_response(user_message, prompt, language)
            elif any(keyword in user_message for keyword in ["设备", "故障", "维护", "状态"]):
                return self._generate_equipment_response(user_message, prompt, language)
            elif any(keyword in user_message for keyword in ["质量", "合格率", "检测", "缺陷"]):
                return self._generate_quality_response(user_message, prompt, language)
            elif any(keyword in user_message for keyword in ["库存", "物料", "PCI", "FIFO"]):
                return self._generate_inventory_response(user_message, prompt, language)
            else:
                return self._generate_general_response(user_message, prompt, language)

    def _generate_production_planning_response(self, user_message: str, prompt: str) -> str:
        """生成生产计划相关响应"""
        return f"""基于当前系统数据分析，我为您提供以下生产计划建议：

📊 **当前状态分析**：
- 生产线L01正常运行，产能50件/小时
- 生产线L02停线（原料短缺），需要调整
- 储罐Tank01-Tank05状态良好

🎯 **优化建议**：
1. **设备调整**：将L02的订单转移到L01或L04
2. **物料优化**：优先消耗超过180天的老旧物料
3. **排程优化**：建议延长L01工作时间补偿L02停线

📈 **预期效果**：
- 总体延期：1-2天
- 产能利用率：提升至85%
- 成本影响：增加5-8%

🚀 **执行建议**：
我建议立即生成新的生产计划，整合当前设备状态和物料约束。是否需要我为您生成详细的生产计划？"""

    def _generate_equipment_response(self, user_message: str, prompt: str) -> str:
        """生成设备相关响应"""
        return f"""关于设备管理，我基于当前数据为您分析：

⚙️ **设备状态概览**：
- L01: 🟢 正常运行 (利用率85.2%)
- L02: 🔴 停线 (原料短缺)
- L03: 🟠 维护中 (预防性维护)
- L04: 🔵 忙碌生产 (利用率76.3%)

🔍 **问题分析**：
- L02停线影响总产能45件/小时
- 需要及时解决原料供应问题
- L03维护预计今晚20:00完成

💡 **建议措施**：
1. 立即联系供应商解决L02原料问题
2. 将L02订单临时分配给L01和L04
3. 监控L03维护进度，确保按时完成
4. 考虑启用备用设备应对紧急情况

是否需要我帮您调整设备配置或生成设备维护计划？"""

    def _generate_quality_response(self, user_message: str, prompt: str) -> str:
        """生成质量相关响应"""
        return f"""关于质量管理，我为您提供以下分析：

📊 **质量状态分析**：
- 整体合格率：98.5%
- 最近7天测试：156次
- 关键质量指标：正常范围内

🎯 **质量趋势**：
- 质量表现稳定向好
- 无重大质量事故
- 持续改进效果显著

💡 **优化建议**：
1. 继续保持当前质量标准
2. 加强过程控制监测
3. 定期校准检测设备
4. 强化员工质量意识培训

📈 **预防措施**：
- 建立质量预警机制
- 实施统计过程控制
- 定期质量审核

是否需要我为您生成详细的质量改进计划？"""

    def _generate_inventory_response(self, user_message: str, prompt: str) -> str:
        """生成库存相关响应"""
        return f"""关于库存管理，我基于PCI数据为您分析：

📦 **库存状态**：
- FS物料总数：245个
- 超过180天物料：12个（需优先消耗）
- 高优先级物料：8个

🔄 **FIFO建议**：
1. 优先安排超龄物料的生产任务
2. 调整生产顺序，确保先进先出
3. 监控物料消耗速度

⚠️ **风险提醒**：
- 部分物料接近过期，需加快消耗
- 建议调整采购计划，避免积压

💡 **优化方案**：
- 制定专项消耗计划
- 优化库存周转率
- 建立预警机制

是否需要我为您生成库存优化计划和消耗策略？"""

    def _generate_general_response(self, user_message: str, prompt: str) -> str:
        """生成通用响应"""
        return f"""感谢您的咨询！作为您的专业生产管理AI助手，我可以为您提供：

🏭 **生产管理服务**：
- 生产计划优化与调整
- 设备状态监控与维护建议
- 质量管理分析与改进
- 库存优化与FIFO管理

📊 **数据分析能力**：
- 实时数据解读
- 趋势分析预测
- 异常检测预警
- 性能指标评估

🎯 **智能决策支持**：
- 基于数据的决策建议
- 多目标优化方案
- 风险评估与预防
- 持续改进建议

请告诉我您具体需要哪方面的帮助，我会基于当前系统数据为您提供专业的分析和建议。

例如：
- "帮我分析当前生产瓶颈"
- "优化明天的生产排程"
- "检查设备维护计划"
- "分析质量数据趋势"
"""

    def _analyze_plan_generation_need(self, user_message: str, llm_response: Dict[str, Any]) -> Dict[str, Any]:
        """分析是否需要生成生产计划"""
        plan_keywords = ["生成计划", "调整计划", "优化排程", "重新安排", "制定计划"]

        message_needs_plan = any(keyword in user_message for keyword in plan_keywords)
        response_suggests_plan = any(keyword in llm_response["content"] for keyword in ["生成", "制定", "调整"])

        if message_needs_plan or response_suggests_plan:
            return {
                "needed": True,
                "type": "production_plan",
                "reason": "用户请求或AI建议生成生产计划",
                "priority": "high" if message_needs_plan else "medium"
            }

        return {"needed": False}

    def _generate_production_plan(self, conversation_id: str, message_id: str,
                                plan_generation: Dict[str, Any], data_context: Dict[str, Any]) -> Dict[str, Any]:
        """生成生产计划"""
        try:
            # 获取算法输入数据
            algorithm_data = self.data_integration.get_algorithm_input_data()

            # 模拟生产计划生成
            plan_data = {
                "plan_id": str(uuid.uuid4()),
                "generated_at": datetime.now().isoformat(),
                "equipment_allocation": {
                    "L01": {"orders": ["A001", "B003"], "utilization": 90},
                    "L04": {"orders": ["B002", "C001"], "utilization": 85}
                },
                "material_consumption": {
                    "priority_items": ["FS001", "FS005", "FS012"],
                    "consumption_order": "FIFO优先"
                },
                "timeline": {
                    "start_date": datetime.now().date().isoformat(),
                    "end_date": (datetime.now() + timedelta(days=7)).date().isoformat()
                },
                "optimization_results": {
                    "efficiency_improvement": "15%",
                    "cost_reduction": "8%",
                    "delivery_performance": "98%"
                }
            }

            # 保存生产计划生成记录
            conn = sqlite3.connect(self.conversation_manager.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO plan_generations (id, conversation_id, message_id, plan_data, generation_type, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                str(uuid.uuid4()),
                conversation_id,
                message_id,
                json.dumps(plan_data),
                plan_generation["type"],
                "completed"
            ))

            conn.commit()
            conn.close()

            return {
                "success": True,
                "plan_data": plan_data,
                "message": "生产计划已成功生成并保存"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"生产计划生成失败: {str(e)}"
            }


# 全局LLM服务实例
enhanced_llm_service = EnhancedLLMService()
