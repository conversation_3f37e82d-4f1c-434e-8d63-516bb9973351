# 🤖 AI助手模板系统扩展指南

## 📋 概述

Smart APS的AI助手模板系统支持两种类型的模板：
- **静态模板**：预定义的提示词，适用于通用场景
- **动态数据模板**：集成数据库查询，自动获取和分析实时数据

## 🔧 模板系统架构

```
AI助手模板系统
├── 静态模板 (Static Templates)
│   ├── 预定义提示词
│   ├── 分类管理
│   └── 权限控制
└── 动态数据模板 (Dynamic Templates)
    ├── 数据源集成
    ├── 参数配置
    ├── 实时数据获取
    └── LLM分析处理
```

## 📝 添加新模板的步骤

### 1. 静态模板扩展

#### 步骤1：定义模板配置
```python
# 在 config/ai_templates.py 中添加
new_static_template = TemplateConfig(
    name="新模板名称",
    type=TemplateType.STATIC,
    description="模板描述",
    category="模板类别",
    prompt_template="您的提示词内容",
    required_permissions=["required.permission"]
)

# 添加到静态模板字典
STATIC_TEMPLATES["新模板名称"] = new_static_template
```

#### 步骤2：更新前端界面
模板会自动出现在智能助手页面的静态模板选择中。

### 2. 动态数据模板扩展（以PCI模板为例）

#### 步骤1：定义模板配置
```python
pci_template = TemplateConfig(
    name="PCI性能分析",
    type=TemplateType.DYNAMIC,
    description="实时分析PCI设备性能数据",
    category="系统性能",
    prompt_template="请对以下PCI性能数据进行{analysis_type}：\n\n{data_content}",
    data_source=DataSource.REALTIME,
    api_endpoint="/api/v1/system/pci-performance",
    parameters={
        "time_ranges": ["最近1小时", "最近24小时", "最近7天"],
        "metrics": ["吞吐量", "延迟", "错误率", "带宽利用率"],
        "analysis_types": ["性能趋势分析", "异常检测", "瓶颈识别"]
    },
    required_permissions=["system.view", "llm.chat"]
)
```

#### 步骤2：创建数据获取函数
```python
def fetch_pci_data(time_range, metrics, device_filter):
    """获取PCI性能数据"""
    try:
        # 调用实际API
        result = api_client.get_pci_performance_data(
            time_range=time_range,
            metrics=metrics,
            device_filter=device_filter
        )
        
        if result.get("success"):
            return result.get("data")
        else:
            # 返回模拟数据作为回退
            return get_mock_pci_data()
            
    except Exception as e:
        logger.error(f"获取PCI数据失败: {str(e)}")
        return get_mock_pci_data()
```

#### 步骤3：创建消息生成函数
```python
def generate_pci_analysis_message(pci_data, analysis_type, metrics, time_range):
    """生成PCI分析消息"""
    # 格式化数据
    data_summary = format_pci_metrics(pci_data, metrics)
    device_info = format_device_status(pci_data.get("devices", []))
    alerts_info = format_alerts(pci_data.get("alerts", []))
    
    # 构建分析消息
    message = f"""
请对以下PCI性能数据进行{analysis_type}（时间范围：{time_range}）：

**性能指标数据**：
{data_summary}

**设备状态**：
{device_info}

**告警信息**：
{alerts_info}

请提供详细的分析结果，包括：
1. 性能趋势分析
2. 潜在问题识别
3. 优化建议
4. 预防措施
"""
    return message
```

#### 步骤4：创建模板界面
```python
def show_pci_analysis_template():
    """PCI性能分析模板界面"""
    st.markdown("#### 📊 PCI性能分析配置")
    
    # 参数选择界面
    time_range = st.selectbox("分析时间范围", 
        ["最近1小时", "最近24小时", "最近7天", "最近30天"])
    
    pci_metrics = st.multiselect("选择PCI指标",
        ["吞吐量", "延迟", "错误率", "带宽利用率", "队列深度", "IOPS"],
        default=["吞吐量", "延迟", "错误率"])
    
    analysis_type = st.selectbox("分析类型",
        ["性能趋势分析", "异常检测", "瓶颈识别", "对比分析"])
    
    if st.button("🚀 开始PCI分析", use_container_width=True):
        with st.spinner("正在获取PCI数据..."):
            # 获取数据并生成分析消息
            pci_data = fetch_pci_data(time_range, pci_metrics, "全部设备")
            
            if pci_data:
                analysis_message = generate_pci_analysis_message(
                    pci_data, analysis_type, pci_metrics, time_range
                )
                
                # 设置模板消息并关闭模态框
                st.session_state.template_message = analysis_message
                st.session_state.show_dynamic_template = False
                st.success("✅ PCI数据获取成功，正在生成分析...")
                st.rerun()
            else:
                st.error("❌ 获取PCI数据失败")
```

#### 步骤5：集成到动态模板系统
```python
# 在 show_dynamic_template_modal() 函数中添加
elif template_name == "PCI性能分析":
    show_pci_analysis_template()
```

## 🔌 API集成指南

### 1. 后端API开发

#### 创建PCI性能API端点
```python
# backend/app/api/v1/endpoints/system.py
@router.get("/pci-performance", summary="获取PCI性能数据")
async def get_pci_performance_data(
    time_range: str = Query(..., description="时间范围"),
    metrics: List[str] = Query(..., description="性能指标"),
    device_filter: str = Query("all", description="设备筛选"),
    current_user: dict = Depends(require_permission("system.view"))
):
    """获取PCI性能数据"""
    try:
        # 实现数据获取逻辑
        pci_service = PCIPerformanceService()
        data = await pci_service.get_performance_data(
            time_range=time_range,
            metrics=metrics,
            device_filter=device_filter
        )
        
        return {
            "success": True,
            "data": data
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取PCI性能数据失败: {str(e)}"
        )
```

### 2. 前端API客户端扩展

```python
# frontend/utils/api_client.py
def get_pci_performance_data(self, time_range: str, metrics: List[str], 
                           device_filter: str = "all") -> Dict[str, Any]:
    """获取PCI性能数据"""
    url = f"{self.base_url}/api/v1/system/pci-performance"
    headers = self._get_headers()
    
    params = {
        "time_range": time_range,
        "metrics": metrics,
        "device_filter": device_filter
    }
    
    try:
        response = self.session.get(url, params=params, headers=headers)
        return self._handle_response(response)
    except Exception as e:
        logger.error(f"获取PCI性能数据失败: {str(e)}")
        return {"success": False, "message": "获取PCI性能数据失败"}
```

## 📊 数据格式规范

### PCI性能数据格式示例
```json
{
  "time_range": "最近24小时",
  "metrics": ["吞吐量", "延迟", "错误率"],
  "data": {
    "吞吐量": {
      "current": 850,
      "average": 780,
      "peak": 950,
      "unit": "MB/s"
    },
    "延迟": {
      "current": 2.3,
      "average": 2.8,
      "peak": 5.1,
      "unit": "ms"
    }
  },
  "devices": [
    {
      "name": "PCI-Device-01",
      "status": "正常",
      "performance": "优秀"
    }
  ],
  "alerts": [
    {
      "level": "警告",
      "message": "PCI-Device-02延迟偏高",
      "time": "10:30"
    }
  ]
}
```

## 🔒 权限管理

### 权限配置
```python
# 在模板配置中指定所需权限
required_permissions=["system.view", "llm.chat"]

# 系统会自动检查用户权限
def check_template_permissions(template: TemplateConfig, user_permissions: List[str]) -> bool:
    if not template.required_permissions:
        return True
    return all(perm in user_permissions for perm in template.required_permissions)
```

## 🎯 最佳实践

### 1. 模板设计原则
- **清晰的提示词**：确保LLM能够理解分析要求
- **结构化数据**：使用一致的数据格式
- **错误处理**：提供回退机制和友好的错误提示
- **权限控制**：合理设置访问权限

### 2. 性能优化
- **数据缓存**：对频繁访问的数据进行缓存
- **异步处理**：使用异步API调用
- **分页加载**：对大量数据进行分页处理
- **超时控制**：设置合理的API超时时间

### 3. 用户体验
- **加载指示**：显示数据获取进度
- **参数验证**：在前端进行参数验证
- **结果展示**：清晰展示分析结果
- **操作反馈**：提供操作成功/失败反馈

## 🚀 扩展示例

通过以上步骤，您可以轻松添加各种类型的动态模板：
- **网络性能分析**
- **数据库性能监控**
- **应用程序性能分析**
- **安全事件分析**
- **业务指标分析**

每个模板都可以集成实时数据，并利用LLM的强大分析能力为用户提供专业的洞察和建议。
