"""
日志配置
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Dict, Any

from app.core.config import settings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


class RequestIdFilter(logging.Filter):
    """请求ID过滤器"""
    
    def filter(self, record):
        # 添加请求ID（如果存在）
        record.request_id = getattr(record, 'request_id', 'N/A')
        return True


def setup_logging():
    """设置日志配置"""
    
    # 创建日志目录
    log_dir = os.path.dirname(settings.LOG_FILE)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # 控制台格式（带颜色）
    console_formatter = ColoredFormatter(
        fmt="%(asctime)s | %(levelname)-8s | %(name)s:%(lineno)d | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(RequestIdFilter())
    
    # 文件处理器（轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.LOG_FILE,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # 文件格式（无颜色）
    file_formatter = logging.Formatter(
        fmt="%(asctime)s | %(levelname)-8s | %(name)s:%(lineno)d | %(request_id)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(file_formatter)
    file_handler.addFilter(RequestIdFilter())
    
    # 添加处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    # 应用启动日志
    logger = logging.getLogger(__name__)
    logger.info("🔧 日志系统初始化完成")
    logger.info(f"📁 日志文件: {settings.LOG_FILE}")
    logger.info(f"📊 日志级别: {settings.LOG_LEVEL}")


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def _log_structured(self, level: int, message: str, **kwargs):
        """记录结构化日志"""
        extra_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "service": "smart_aps",
            **kwargs
        }
        
        # 格式化消息
        if extra_data:
            formatted_message = f"{message} | {extra_data}"
        else:
            formatted_message = message
        
        self.logger.log(level, formatted_message, extra=extra_data)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self._log_structured(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self._log_structured(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log_structured(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self._log_structured(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self._log_structured(logging.CRITICAL, message, **kwargs)


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志器"""
    return StructuredLogger(name)


# 业务日志记录器
class BusinessLogger:
    """业务日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("business")
    
    def log_user_action(self, user_id: str, action: str, details: Dict[str, Any] = None):
        """记录用户操作"""
        self.logger.info(
            f"用户操作: {action}",
            user_id=user_id,
            action=action,
            details=details or {}
        )
    
    def log_system_event(self, event_type: str, details: Dict[str, Any] = None):
        """记录系统事件"""
        self.logger.info(
            f"系统事件: {event_type}",
            event_type=event_type,
            details=details or {}
        )
    
    def log_performance(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """记录性能指标"""
        self.logger.info(
            f"性能指标: {operation} 耗时 {duration:.2f}秒",
            operation=operation,
            duration=duration,
            details=details or {}
        )
    
    def log_error(self, error_type: str, error_message: str, details: Dict[str, Any] = None):
        """记录错误"""
        self.logger.error(
            f"业务错误: {error_type} - {error_message}",
            error_type=error_type,
            error_message=error_message,
            details=details or {}
        )


# 全局业务日志器
business_logger = BusinessLogger()
