"""
强化学习功能测试
"""

import sys
import os
import unittest
from datetime import datetime
import numpy as np

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from learning_engine import (
        SchedulingState, SchedulingAction, RLExperience,
        DQNNetwork, ReinforcementLearningScheduler, learning_engine
    )
    from reinforcement_learning_service import rl_scheduling_service, SchedulingEnvironment
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestReinforcementLearning(unittest.TestCase):
    """强化学习测试类"""
    
    def setUp(self):
        """测试设置"""
        self.orders = [
            {"id": "ORD001", "quantity": 100, "priority": 1},
            {"id": "ORD002", "quantity": 150, "priority": 2},
            {"id": "ORD003", "quantity": 80, "priority": 3}
        ]
        
        self.equipment = {
            "L01": {"available": True, "capacity": 200},
            "L02": {"available": True, "capacity": 180},
            "L03": {"available": False, "capacity": 150}
        }
    
    def test_scheduling_state_creation(self):
        """测试排程状态创建"""
        state = SchedulingState(
            orders=self.orders,
            equipment_status=self.equipment,
            resource_availability={},
            time_step=0,
            current_makespan=0.0,
            utilization_rate=0.0
        )
        
        self.assertEqual(len(state.orders), 3)
        self.assertEqual(len(state.equipment_status), 3)
        self.assertEqual(state.time_step, 0)
        
        # 测试向量化
        vector = state.to_vector()
        self.assertIsInstance(vector, np.ndarray)
        self.assertEqual(len(vector), 8)  # 预期的向量维度
    
    def test_scheduling_action_creation(self):
        """测试排程动作创建"""
        action = SchedulingAction(
            order_id="ORD001",
            equipment_id="L01",
            start_time=0.0,
            priority_adjustment=0.1
        )
        
        self.assertEqual(action.order_id, "ORD001")
        self.assertEqual(action.equipment_id, "L01")
        
        # 测试向量化
        vector = action.to_vector()
        self.assertIsInstance(vector, np.ndarray)
        self.assertEqual(len(vector), 4)  # 预期的向量维度
    
    def test_rl_experience_creation(self):
        """测试强化学习经验创建"""
        state = SchedulingState(
            orders=self.orders,
            equipment_status=self.equipment,
            resource_availability={},
            time_step=0,
            current_makespan=0.0,
            utilization_rate=0.0
        )
        
        action = SchedulingAction(
            order_id="ORD001",
            equipment_id="L01",
            start_time=0.0
        )
        
        next_state = SchedulingState(
            orders=self.orders[1:],  # 移除一个订单
            equipment_status=self.equipment,
            resource_availability={},
            time_step=1,
            current_makespan=2.0,
            utilization_rate=0.1
        )
        
        experience = RLExperience(
            state=state,
            action=action,
            reward=10.0,
            next_state=next_state,
            done=False,
            timestamp=datetime.now()
        )
        
        self.assertEqual(experience.reward, 10.0)
        self.assertFalse(experience.done)
        self.assertIsInstance(experience.timestamp, datetime)
    
    def test_dqn_network_creation(self):
        """测试DQN网络创建"""
        dqn = DQNNetwork(state_dim=8, action_dim=100)
        
        self.assertEqual(dqn.state_dim, 8)
        self.assertEqual(dqn.action_dim, 100)
        
        # 测试预测
        state_vector = np.random.randn(8)
        q_values = dqn.predict(state_vector)
        
        self.assertIsInstance(q_values, np.ndarray)
        self.assertEqual(len(q_values), 100)
    
    def test_rl_scheduler_creation(self):
        """测试强化学习排程器创建"""
        scheduler = ReinforcementLearningScheduler()
        
        self.assertEqual(scheduler.state_dim, 8)
        self.assertEqual(scheduler.action_dim, 100)
        self.assertGreater(scheduler.epsilon, 0)
        self.assertEqual(len(scheduler.experience_buffer), 0)
    
    def test_rl_scheduler_action_selection(self):
        """测试动作选择"""
        scheduler = ReinforcementLearningScheduler()
        
        state = SchedulingState(
            orders=self.orders,
            equipment_status=self.equipment,
            resource_availability={},
            time_step=0,
            current_makespan=0.0,
            utilization_rate=0.0
        )
        
        available_actions = [
            SchedulingAction("ORD001", "L01", 0.0),
            SchedulingAction("ORD002", "L02", 0.0)
        ]
        
        selected_action = scheduler.select_action(state, available_actions)
        self.assertIn(selected_action, available_actions)
    
    def test_scheduling_environment(self):
        """测试排程环境"""
        env = SchedulingEnvironment(
            orders=self.orders,
            equipment=self.equipment,
            resources={}
        )
        
        # 测试重置
        initial_state = env.reset()
        self.assertEqual(len(initial_state.orders), 3)
        self.assertEqual(initial_state.time_step, 0)
        
        # 测试步骤执行
        action = SchedulingAction("ORD001", "L01", 0.0)
        next_state, reward, done = env.step(initial_state, action)
        
        self.assertIsInstance(reward, float)
        self.assertIsInstance(done, bool)
        self.assertEqual(next_state.time_step, 1)
    
    def test_rl_scheduling_service(self):
        """测试强化学习排程服务"""
        # 测试环境设置
        success = rl_scheduling_service.setup_environment(self.orders, self.equipment)
        self.assertTrue(success)
        
        # 测试训练状态获取
        status = rl_scheduling_service.get_training_status()
        self.assertIn("is_training", status)
        self.assertIn("environment_ready", status)
        self.assertTrue(status["environment_ready"])
        
        # 测试排程生成
        result = rl_scheduling_service.generate_optimized_schedule(self.orders, self.equipment)
        self.assertIn("success", result)
    
    def test_learning_engine_rl_integration(self):
        """测试学习引擎强化学习集成"""
        # 测试强化学习状态获取
        rl_status = learning_engine.get_rl_training_status()
        
        self.assertIn("rl_enabled", rl_status)
        self.assertIn("deep_learning_enabled", rl_status)
        self.assertIn("episode_count", rl_status)
        self.assertIn("experience_count", rl_status)
        
        # 测试配置更新
        learning_engine.update_learning_config({
            "rl_enabled": True,
            "deep_learning_enabled": True
        })
        
        updated_status = learning_engine.get_rl_training_status()
        self.assertTrue(updated_status["rl_enabled"])
        self.assertTrue(updated_status["deep_learning_enabled"])
    
    def test_reward_calculation(self):
        """测试奖励计算"""
        scheduler = ReinforcementLearningScheduler()
        
        state = SchedulingState(
            orders=self.orders,
            equipment_status=self.equipment,
            resource_availability={},
            time_step=0,
            current_makespan=10.0,
            utilization_rate=0.5
        )
        
        action = SchedulingAction("ORD001", "L01", 0.0)
        
        next_state = SchedulingState(
            orders=self.orders[1:],  # 完成一个订单
            equipment_status=self.equipment,
            resource_availability={},
            time_step=1,
            current_makespan=8.0,  # 改进了完工时间
            utilization_rate=0.6   # 提高了利用率
        )
        
        reward = scheduler.calculate_reward(state, action, next_state)
        self.assertIsInstance(reward, float)
        self.assertGreater(reward, 0)  # 应该是正奖励，因为有改进


def run_tests():
    """运行所有测试"""
    print("🧠 开始强化学习功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestReinforcementLearning)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有强化学习测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    run_tests()
