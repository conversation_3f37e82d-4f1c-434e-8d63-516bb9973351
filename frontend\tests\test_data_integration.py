"""
数据集成功能测试脚本
验证数据集成服务的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_data_integration_service():
    """测试数据集成服务"""
    print("🔍 开始测试数据集成服务...")
    
    try:
        # 导入服务
        from services.data_integration_service import DataIntegrationService
        
        # 创建服务实例
        service = DataIntegrationService()
        print("✅ 数据集成服务创建成功")
        
        # 测试获取可用数据源
        sources = service.get_available_data_sources()
        print(f"✅ 获取数据源成功，共 {len(sources)} 个数据源")
        
        # 测试添加自定义数据源
        test_config = {
            "endpoint": "http://test.api.com",
            "api_key": "test_key",
            "timeout": 30
        }
        
        service.add_custom_data_source(
            source_name="test_api",
            source_type="api",
            config=test_config
        )
        print("✅ 添加自定义数据源成功")
        
        # 测试记录用户输入
        service.record_user_input(
            input_type="test_input",
            input_data={"test": "data"},
            source_page="test_page"
        )
        print("✅ 记录用户输入成功")
        
        # 测试获取综合数据上下文
        context = service.get_comprehensive_data_context()
        print("✅ 获取综合数据上下文成功")
        print(f"   - 数据源数量: {len(context.get('data_sources', {}))}")
        print(f"   - 约束条件数量: {len(context.get('constraints', []))}")
        print(f"   - 建议数量: {len(context.get('recommendations', []))}")
        
        # 测试获取LLM上下文
        llm_context = service.get_llm_prompt_context()
        print("✅ 获取LLM上下文成功")
        print(f"   - 上下文长度: {len(llm_context)} 字符")
        
        # 测试获取算法输入数据
        algorithm_data = service.get_algorithm_input_data()
        print("✅ 获取算法输入数据成功")
        print(f"   - 数据结构包含: {list(algorithm_data.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_data_source_plugins():
    """测试数据源插件"""
    print("\n🔍 开始测试数据源插件...")
    
    try:
        from services.data_source_plugins import ERPDataSourcePlugin, IoTSensorPlugin, QualityDataPlugin
        
        # 测试ERP插件
        erp_plugin = ERPDataSourcePlugin("http://test.erp.com", "test_key")
        erp_data = erp_plugin.get_data_context()
        print("✅ ERP插件测试成功")
        print(f"   - 订单数量: {len(erp_data.get('orders', []))}")
        print(f"   - 库存数量: {len(erp_data.get('inventory', []))}")
        
        # 测试IoT插件
        iot_plugin = IoTSensorPlugin({"sensors": ["TEMP001", "PRES001"]})
        iot_data = iot_plugin.get_data_context()
        print("✅ IoT插件测试成功")
        print(f"   - 传感器数量: {len(iot_data.get('sensors', []))}")
        
        # 测试质量插件
        quality_plugin = QualityDataPlugin()
        quality_data = quality_plugin.get_data_context()
        print("✅ 质量插件测试成功")
        print(f"   - 质量统计: {quality_data.get('quality_statistics', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_data_validation():
    """测试数据验证功能"""
    print("\n🔍 开始测试数据验证...")
    
    try:
        from services.data_integration_service import DataIntegrationService
        
        service = DataIntegrationService()
        
        # 测试有效配置
        valid_config = {
            "connection": {
                "type": "mysql",
                "host": "localhost",
                "database": "test_db"
            }
        }
        
        result = service._validate_data_source_config("database", valid_config)
        assert result["valid"] == True
        print("✅ 有效配置验证通过")
        
        # 测试无效配置
        invalid_config = {
            "connection": {
                "type": "mysql"
                # 缺少必需字段
            }
        }
        
        result = service._validate_data_source_config("database", invalid_config)
        assert result["valid"] == False
        print("✅ 无效配置验证通过")
        
        # 测试添加数据源时的验证
        try:
            service.add_custom_data_source(
                source_name="",  # 空名称
                source_type="api",
                config={"endpoint": "http://test.com"}
            )
            print("❌ 应该抛出异常但没有")
            return False
        except ValueError:
            print("✅ 空名称验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🔍 开始测试错误处理...")
    
    try:
        from services.data_integration_service import DataIntegrationService
        
        service = DataIntegrationService()
        
        # 测试不支持的数据源类型
        try:
            service.add_custom_data_source(
                source_name="test",
                source_type="unsupported_type",
                config={}
            )
            print("❌ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✅ 不支持的类型错误处理正确: {str(e)}")
        
        # 测试记录用户输入的错误处理
        # 这个应该不会抛出异常，而是静默处理
        service.record_user_input(
            input_type="test",
            input_data={"complex": {"nested": "data"}},
            source_page="test"
        )
        print("✅ 用户输入记录错误处理正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始数据集成功能测试")
    print("=" * 50)
    
    tests = [
        ("数据集成服务", test_data_integration_service),
        ("数据源插件", test_data_source_plugins),
        ("数据验证", test_data_validation),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据集成功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
