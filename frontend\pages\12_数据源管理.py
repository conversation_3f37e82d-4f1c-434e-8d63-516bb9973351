"""
数据源管理页面
管理和配置系统的数据源
"""

import streamlit as st
import json
from datetime import datetime

from utils.auth import check_authentication, require_permission
from services.data_integration_service import data_integration_service
from services.data_source_plugins import register_example_plugins

# 页面配置
st.set_page_config(
    page_title="数据源管理 - Smart APS",
    page_icon="🔌",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("admin.manage"):
    st.error("权限不足，需要管理员权限")
    st.stop()

# 页面标题
st.title("🔌 数据源管理")
st.markdown("### 管理和配置系统的所有数据源")

# 侧边栏 - 数据源类型
with st.sidebar:
    st.markdown("### 📊 数据源类型")

    data_source_types = {
        "core": "🏗️ 核心数据源",
        "plugin": "🔌 插件数据源",
        "database": "🗄️ 数据库",
        "api": "🌐 API接口",
        "file": "📁 文件数据源",
        "sensor": "📡 传感器数据"
    }

    for type_key, type_name in data_source_types.items():
        st.markdown(f"- {type_name}")

    st.markdown("---")

    if st.button("🔄 刷新数据源", use_container_width=True):
        st.rerun()

    if st.button("📥 注册示例插件", use_container_width=True):
        register_example_plugins(data_integration_service)
        st.success("示例插件已注册")
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["📋 数据源列表", "➕ 添加数据源", "⚙️ 配置管理", "🔍 测试连接"])

with tab1:
    st.markdown("#### 📋 当前数据源")

    # 获取所有数据源
    available_sources = data_integration_service.get_available_data_sources()

    if available_sources:
        # 按类型分组显示
        core_sources = [s for s in available_sources if s["type"] == "core"]
        plugin_sources = [s for s in available_sources if s["type"] == "plugin"]
        custom_sources = [s for s in available_sources if s["type"] not in ["core", "plugin"]]

        # 核心数据源
        if core_sources:
            st.markdown("##### 🏗️ 核心数据源")
            for source in core_sources:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        status_icon = "✅" if source["enabled"] else "❌"
                        st.write(f"{status_icon} **{source['display_name']}**")
                        st.caption(source["description"])

                    with col2:
                        st.write(source["type"])

                    with col3:
                        if source["enabled"]:
                            st.success("启用")
                        else:
                            st.error("禁用")

                    with col4:
                        st.write("系统内置")

                    st.markdown("---")

        # 插件数据源
        if plugin_sources:
            st.markdown("##### 🔌 插件数据源")
            for source in plugin_sources:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        status_icon = "✅" if source["enabled"] else "❌"
                        st.write(f"{status_icon} **{source['display_name']}**")
                        st.caption(source["description"])

                    with col2:
                        st.write(source["type"])

                    with col3:
                        if source["enabled"]:
                            st.success("启用")
                        else:
                            st.error("禁用")

                    with col4:
                        if st.button("🔧 配置", key=f"config_{source['name']}"):
                            st.session_state.config_source = source

                    st.markdown("---")

        # 自定义数据源
        if custom_sources:
            st.markdown("##### 🛠️ 自定义数据源")
            for source in custom_sources:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        status_icon = "✅" if source["enabled"] else "❌"
                        st.write(f"{status_icon} **{source['display_name']}**")
                        st.caption(source["description"])

                    with col2:
                        st.write(source["type"])

                    with col3:
                        if source["enabled"]:
                            st.success("启用")
                        else:
                            st.error("禁用")

                    with col4:
                        col_edit, col_delete = st.columns(2)
                        with col_edit:
                            if st.button("✏️", key=f"edit_{source['name']}", help="编辑"):
                                st.session_state.edit_source = source
                        with col_delete:
                            if st.button("🗑️", key=f"delete_{source['name']}", help="删除"):
                                st.session_state.delete_source = source

                    st.markdown("---")
    else:
        st.info("暂无数据源")

with tab2:
    st.markdown("#### ➕ 添加新数据源")

    # 数据源类型选择
    source_type = st.selectbox(
        "数据源类型",
        ["database", "api", "file", "sensor"],
        format_func=lambda x: {
            "database": "🗄️ 数据库",
            "api": "🌐 API接口",
            "file": "📁 文件数据源",
            "sensor": "📡 传感器数据"
        }[x]
    )

    with st.form("add_data_source_form"):
        source_name = st.text_input("数据源名称", placeholder="例如：生产数据库")
        description = st.text_area("描述", placeholder="描述数据源的用途和内容")

        # 根据类型显示不同的配置选项
        if source_type == "database":
            st.markdown("##### 🗄️ 数据库配置")
            db_type = st.selectbox("数据库类型", ["MySQL", "PostgreSQL", "SQL Server", "Oracle"])
            host = st.text_input("主机地址", placeholder="localhost")
            port = st.number_input("端口", value=3306 if db_type == "MySQL" else 5432)
            database = st.text_input("数据库名", placeholder="production_db")
            username = st.text_input("用户名")
            password = st.text_input("密码", type="password")

            config = {
                "connection": {
                    "type": db_type.lower(),
                    "host": host,
                    "port": port,
                    "database": database,
                    "username": username,
                    "password": password
                },
                "tables": st.text_area("相关表名（每行一个）", placeholder="orders\ninventory\nproduction").split('\n')
            }

        elif source_type == "api":
            st.markdown("##### 🌐 API配置")
            endpoint = st.text_input("API端点", placeholder="https://api.example.com/v1/data")
            api_key = st.text_input("API密钥", type="password")
            headers = st.text_area("请求头（JSON格式）", value='{"Content-Type": "application/json"}')

            try:
                parsed_headers = json.loads(headers) if headers else {}
            except json.JSONDecodeError:
                parsed_headers = {}
                st.warning("请求头JSON格式错误，将使用默认值")

            config = {
                "endpoint": endpoint,
                "api_key": api_key,
                "headers": parsed_headers,
                "timeout": st.number_input("超时时间(秒)", value=30)
            }

        elif source_type == "file":
            st.markdown("##### 📁 文件配置")
            file_path = st.text_input("文件路径", placeholder="/data/production/daily_report.csv")
            file_format = st.selectbox("文件格式", ["csv", "excel", "json", "xml"])
            encoding = st.selectbox("编码", ["utf-8", "gbk", "gb2312"])

            config = {
                "file_path": file_path,
                "format": file_format,
                "encoding": encoding,
                "update_frequency": st.selectbox("更新频率", ["real-time", "hourly", "daily", "weekly"])
            }

        elif source_type == "sensor":
            st.markdown("##### 📡 传感器配置")
            sensor_protocol = st.selectbox("通信协议", ["MQTT", "HTTP", "Modbus", "OPC-UA"])
            sensor_address = st.text_input("传感器地址", placeholder="*************")
            sensor_port = st.number_input("端口", value=1883)

            config = {
                "protocol": sensor_protocol,
                "address": sensor_address,
                "port": sensor_port,
                "sensors": st.text_area("传感器ID列表（每行一个）", placeholder="TEMP001\nPRES001\nVIBR001").split('\n')
            }

        # 提交按钮
        submitted = st.form_submit_button("➕ 添加数据源", type="primary")

        if submitted:
            if source_name and config:
                try:
                    data_integration_service.add_custom_data_source(
                        source_name=source_name,
                        source_type=source_type,
                        config=config,
                        enabled=True
                    )
                    st.success(f"数据源 '{source_name}' 添加成功！")
                    st.rerun()
                except Exception as e:
                    st.error(f"添加数据源失败: {str(e)}")
            else:
                st.error("请填写完整的配置信息")

with tab3:
    st.markdown("#### ⚙️ 配置管理")

    # 全局配置
    st.markdown("##### 🌐 全局配置")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**数据同步设置**")
        sync_interval = st.selectbox("同步间隔", ["实时", "每分钟", "每5分钟", "每小时"])
        auto_retry = st.checkbox("自动重试", value=True)
        max_retries = st.number_input("最大重试次数", value=3, min_value=1, max_value=10)

    with col2:
        st.markdown("**缓存设置**")
        enable_cache = st.checkbox("启用缓存", value=True)
        cache_ttl = st.number_input("缓存过期时间(分钟)", value=30, min_value=1)
        max_cache_size = st.selectbox("最大缓存大小", ["100MB", "500MB", "1GB", "5GB"])

    if st.button("💾 保存全局配置"):
        global_config = {
            "sync_interval": sync_interval,
            "auto_retry": auto_retry,
            "max_retries": max_retries,
            "enable_cache": enable_cache,
            "cache_ttl": cache_ttl,
            "max_cache_size": max_cache_size
        }
        st.success("全局配置已保存")

    st.markdown("---")

    # 数据源优先级
    st.markdown("##### 📊 数据源优先级")
    st.info("拖拽调整数据源的优先级顺序（功能开发中）")

    available_sources = data_integration_service.get_available_data_sources()
    for i, source in enumerate(available_sources):
        col1, col2, col3 = st.columns([1, 4, 1])
        with col1:
            st.write(f"{i+1}")
        with col2:
            st.write(source["display_name"])
        with col3:
            if i > 0:
                st.button("⬆️", key=f"up_{source['name']}")

with tab4:
    st.markdown("#### 🔍 测试连接")

    # 选择要测试的数据源
    available_sources = data_integration_service.get_available_data_sources()

    if available_sources:
        test_source = st.selectbox(
            "选择要测试的数据源",
            available_sources,
            format_func=lambda x: f"{x['display_name']} ({x['type']})"
        )

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔍 测试连接", type="primary"):
                with st.spinner("正在测试连接..."):
                    # 模拟连接测试
                    import time
                    time.sleep(2)

                    if test_source["type"] == "core":
                        st.success("✅ 核心数据源连接正常")
                    else:
                        st.success(f"✅ {test_source['display_name']} 连接成功")

                    # 显示测试结果
                    st.json({
                        "status": "connected",
                        "response_time": "156ms",
                        "last_data_update": datetime.now().isoformat(),
                        "data_quality": "good"
                    })

        with col2:
            if st.button("📊 获取数据样本"):
                with st.spinner("正在获取数据样本..."):
                    # 模拟获取数据样本
                    import time
                    time.sleep(1)

                    st.success("✅ 数据样本获取成功")

                    # 显示样本数据
                    sample_data = {
                        "sample_size": 10,
                        "data_types": ["string", "number", "datetime"],
                        "sample_records": [
                            {"id": 1, "name": "样本数据1", "value": 123.45},
                            {"id": 2, "name": "样本数据2", "value": 678.90}
                        ]
                    }
                    st.json(sample_data)
    else:
        st.info("暂无可测试的数据源")

# 处理模态框操作
if 'delete_source' in st.session_state:
    source = st.session_state.delete_source
    if st.button(f"确认删除数据源 '{source['display_name']}'？"):
        # 这里应该调用删除API
        st.success(f"数据源 '{source['display_name']}' 已删除")
        del st.session_state.delete_source
        st.rerun()

    if st.button("取消删除"):
        del st.session_state.delete_source
        st.rerun()
