"""
智能规划引擎扩展管理页面
演示如何扩展规划引擎，添加物流、质量、环保等新的规划因素
"""

import streamlit as st
import json
import pandas as pd
from datetime import datetime, timedelta

from utils.auth import check_authentication, require_permission
from services.algorithm_planning_service import algorithm_planning_service
from services.logistics_planning_plugin import logistics_plugin
from services.data_integration_service import data_integration_service

# 页面配置
st.set_page_config(
    page_title="规划引擎扩展 - Smart APS",
    page_icon="🔧",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 页面标题
st.title("🔧 智能规划引擎扩展管理")
st.markdown("### 扩展规划引擎，添加物流、质量、环保等新的规划因素")

# 侧边栏 - 扩展管理
with st.sidebar:
    st.markdown("### ⚙️ 扩展管理")

    # 插件注册
    st.markdown("#### 📦 插件注册")

    if st.button("🚚 注册物流插件", use_container_width=True):
        algorithm_planning_service.register_planning_plugin("logistics", logistics_plugin)
        st.success("物流插件已注册！")
        st.rerun()

    # 可用插件列表
    available_plugins = algorithm_planning_service.get_available_planning_plugins()

    if available_plugins:
        st.markdown("#### 🔌 已注册插件")
        for plugin in available_plugins:
            with st.expander(f"📦 {plugin['name']}"):
                st.write(f"**描述**: {plugin['description']}")
                st.write(f"**版本**: {plugin['version']}")

                enabled = st.checkbox(
                    "启用插件",
                    value=plugin['enabled'],
                    key=f"enable_{plugin['name']}"
                )

                if enabled != plugin['enabled']:
                    algorithm_planning_service.enable_planning_plugin(plugin['name'], enabled)
                    st.rerun()
    else:
        st.info("暂无已注册的插件")

# 主要内容区域
tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(["🔧 扩展概览", "🚚 物流扩展", "⚙️ 可视化配置", "📋 模板管理", "📊 扩展效果", "💡 开发指南"])

with tab1:
    st.markdown("#### 🔧 规划引擎扩展架构")

    # 扩展架构图
    st.markdown("""
    ```
    智能规划引擎扩展架构
    ├── 核心规划引擎 (Core Planning Engine)
    │   ├── 产能计算引擎
    │   ├── 需求计算引擎
    │   ├── 约束条件管理
    │   └── 优化算法
    ├── 插件接口层 (Plugin Interface)
    │   ├── 约束插件接口
    │   ├── 数据源插件接口
    │   ├── 算法插件接口
    │   └── 评估插件接口
    └── 扩展插件 (Extension Plugins)
        ├── 🚚 物流规划插件
        ├── 🌱 环保约束插件
        ├── 💰 成本优化插件
        ├── 👥 人力资源插件
        └── 🔬 质量管理插件
    ```
    """)

    # 当前扩展状态
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("已注册插件", len(available_plugins))

    with col2:
        enabled_plugins = len([p for p in available_plugins if p['enabled']])
        st.metric("启用插件", enabled_plugins)

    with col3:
        st.metric("扩展类型", "4种", help="物流、质量、环保、成本")

    # 扩展能力说明
    st.markdown("#### 🎯 扩展能力")

    expansion_capabilities = [
        {
            "类型": "🚚 物流扩展",
            "功能": "运输路线优化、仓储分配、配送计划",
            "约束": "运输能力、仓储容量、配送时效",
            "优化目标": "物流成本最小化、配送效率最大化"
        },
        {
            "类型": "🌱 环保扩展",
            "功能": "碳排放计算、能耗优化、废料处理",
            "约束": "排放限额、能耗标准、环保法规",
            "优化目标": "碳足迹最小化、绿色生产"
        },
        {
            "类型": "💰 成本扩展",
            "功能": "全成本核算、成本分摊、盈利分析",
            "约束": "预算限制、成本控制、利润目标",
            "优化目标": "总成本最小化、利润最大化"
        },
        {
            "类型": "👥 人力扩展",
            "功能": "人员排班、技能匹配、培训计划",
            "约束": "人员数量、技能要求、工时限制",
            "优化目标": "人力成本优化、技能利用最大化"
        }
    ]

    df_capabilities = pd.DataFrame(expansion_capabilities)
    st.dataframe(df_capabilities, use_container_width=True, hide_index=True)

with tab2:
    st.markdown("#### 🚚 物流规划扩展演示")

    # 物流插件状态
    logistics_enabled = any(p['name'] == 'logistics' and p['enabled'] for p in available_plugins)

    if logistics_enabled:
        st.success("✅ 物流插件已启用")

        # 获取物流数据
        try:
            logistics_data = logistics_plugin.get_data_context()

            # 运输路线信息
            st.markdown("##### 🛣️ 运输路线")
            transport_routes = logistics_data.get("transport_routes", [])

            if transport_routes:
                df_routes = pd.DataFrame(transport_routes)
                st.dataframe(df_routes, use_container_width=True, hide_index=True)

            # 仓库信息
            st.markdown("##### 🏭 仓库状态")
            warehouses = logistics_data.get("warehouses", [])

            if warehouses:
                col1, col2 = st.columns(2)

                with col1:
                    df_warehouses = pd.DataFrame(warehouses)
                    st.dataframe(df_warehouses[['code', 'name', 'location', 'utilization_rate']],
                               use_container_width=True, hide_index=True)

                with col2:
                    # 仓库利用率图表
                    import plotly.express as px

                    fig = px.bar(
                        df_warehouses,
                        x='code',
                        y='utilization_rate',
                        title='仓库利用率',
                        labels={'utilization_rate': '利用率 (%)', 'code': '仓库代码'}
                    )
                    st.plotly_chart(fig, use_container_width=True)

            # 物流约束
            st.markdown("##### ⚠️ 物流约束条件")
            try:
                data_context = data_integration_service.get_comprehensive_data_context()
                logistics_constraints = logistics_plugin.get_constraints(data_context)

                if logistics_constraints:
                    for constraint in logistics_constraints:
                        if constraint.get("urgency") == "high":
                            st.error(f"🔴 {constraint['description']}")
                        elif constraint.get("urgency") == "medium":
                            st.warning(f"🟡 {constraint['description']}")
                        else:
                            st.info(f"🔵 {constraint['description']}")
                else:
                    st.success("✅ 当前无物流约束问题")

            except Exception as e:
                st.error(f"获取物流约束失败: {str(e)}")

            # 物流优化建议
            st.markdown("##### 💡 物流优化建议")
            try:
                data_context = data_integration_service.get_comprehensive_data_context()
                logistics_recommendations = logistics_plugin.get_recommendations(data_context)

                if logistics_recommendations:
                    for i, rec in enumerate(logistics_recommendations):
                        st.info(f"{i+1}. {rec}")
                else:
                    st.success("✅ 当前物流运行良好，无需特别优化")

            except Exception as e:
                st.error(f"获取物流建议失败: {str(e)}")

        except Exception as e:
            st.error(f"获取物流数据失败: {str(e)}")

    else:
        st.warning("⚠️ 物流插件未启用，请在侧边栏注册并启用物流插件")

        # 物流扩展功能预览
        st.markdown("##### 📋 物流扩展功能预览")

        preview_features = [
            "🛣️ 运输路线优化 - 自动选择最优运输路径",
            "🏭 仓储分配优化 - 智能分配仓储资源",
            "📅 配送计划优化 - 优化配送时间和路线",
            "💰 物流成本控制 - 最小化总物流成本",
            "⏰ 交付时效保障 - 确保按时交付",
            "📊 物流KPI监控 - 实时监控物流绩效"
        ]

        for feature in preview_features:
            st.markdown(f"- {feature}")

with tab3:
    st.markdown("#### ⚙️ 可视化扩展配置")

    # 配置选择
    config_type = st.selectbox(
        "选择配置类型",
        ["物流配置", "环保配置", "成本配置", "人力配置", "质量配置"],
        help="选择要配置的扩展类型"
    )

    if config_type == "物流配置":
        show_logistics_config()
    elif config_type == "环保配置":
        show_environmental_config()
    elif config_type == "成本配置":
        show_cost_config()
    elif config_type == "人力配置":
        show_hr_config()
    elif config_type == "质量配置":
        show_quality_config()

with tab4:
    st.markdown("#### 📋 模板管理")

    # 导入配置服务
    from services.extension_config_service import extension_config_service

    # 模板管理选项
    template_action = st.selectbox(
        "选择操作",
        ["查看模板", "创建模板", "编辑模板", "删除模板"],
        help="选择要执行的模板管理操作"
    )

    if template_action == "查看模板":
        show_template_list()
    elif template_action == "创建模板":
        create_new_template()
    elif template_action == "编辑模板":
        edit_existing_template()
    elif template_action == "删除模板":
        delete_template()

with tab5:
    st.markdown("#### 📊 扩展效果分析")

    # 模拟扩展前后对比
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📈 扩展前 (基础规划)")

        basic_metrics = {
            "生产效率": 75.2,
            "设备利用率": 78.5,
            "交付准时率": 85.3,
            "总成本指数": 100,
            "客户满意度": 82.1
        }

        for metric, value in basic_metrics.items():
            st.metric(metric, f"{value}%")

    with col2:
        st.markdown("##### 🚀 扩展后 (集成物流)")

        enhanced_metrics = {
            "生产效率": 82.7,
            "设备利用率": 85.2,
            "交付准时率": 94.8,
            "总成本指数": 92,
            "客户满意度": 91.5
        }

        for metric, value in enhanced_metrics.items():
            improvement = value - basic_metrics[metric]
            delta_color = "normal" if improvement >= 0 else "inverse"
            st.metric(
                metric,
                f"{value}%",
                delta=f"{improvement:+.1f}%",
                delta_color=delta_color
            )

    # 扩展效果图表
    st.markdown("##### 📊 扩展效果对比")

    import plotly.graph_objects as go

    categories = list(basic_metrics.keys())
    basic_values = list(basic_metrics.values())
    enhanced_values = list(enhanced_metrics.values())

    fig = go.Figure()

    fig.add_trace(go.Scatterpolar(
        r=basic_values,
        theta=categories,
        fill='toself',
        name='基础规划',
        line_color='blue'
    ))

    fig.add_trace(go.Scatterpolar(
        r=enhanced_values,
        theta=categories,
        fill='toself',
        name='扩展后',
        line_color='red'
    ))

    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=True,
        title="规划引擎扩展效果对比"
    )

    st.plotly_chart(fig, use_container_width=True)

with tab6:
    st.markdown("#### 💡 扩展开发指南")

    # 开发步骤
    st.markdown("##### 🛠️ 插件开发步骤")

    development_steps = [
        {
            "步骤": "1. 继承基础插件类",
            "说明": "继承 DataSourcePlugin 基类",
            "代码": "class MyPlugin(DataSourcePlugin):"
        },
        {
            "步骤": "2. 实现必要方法",
            "说明": "实现 get_data_context, get_constraints, get_recommendations",
            "代码": "def get_constraints(self, data_context): ..."
        },
        {
            "步骤": "3. 注册插件",
            "说明": "将插件注册到规划引擎",
            "代码": "algorithm_planning_service.register_planning_plugin('my_plugin', plugin_instance)"
        },
        {
            "步骤": "4. 测试验证",
            "说明": "测试插件功能和集成效果",
            "代码": "# 运行测试用例"
        }
    ]

    for step in development_steps:
        with st.expander(f"📋 {step['步骤']}"):
            st.write(step['说明'])
            st.code(step['代码'], language='python')

    # 插件接口说明
    st.markdown("##### 🔌 插件接口说明")

    interface_docs = """
    ```python
    class PlanningPlugin:
        def get_data_context(self) -> Dict[str, Any]:
            '''获取插件数据上下文'''
            pass

        def get_constraints(self, data_context: Dict[str, Any]) -> List[Dict[str, Any]]:
            '''获取约束条件'''
            pass

        def get_recommendations(self, data_context: Dict[str, Any]) -> List[str]:
            '''获取优化建议'''
            pass

        def optimize_plan(self, production_plan: Dict[str, Any]) -> Dict[str, Any]:
            '''优化生产计划（可选）'''
            pass
    ```
    """

    st.markdown(interface_docs)

    # 示例插件代码
    st.markdown("##### 📝 示例插件代码")

    example_code = '''
# 环保约束插件示例
class EnvironmentalPlugin(DataSourcePlugin):
    def __init__(self):
        self.name = "environmental"
        self.description = "环保约束和碳排放优化"

    def get_constraints(self, data_context):
        constraints = []

        # 碳排放限制
        constraints.append({
            "type": "carbon_emission",
            "max_emission": 1000,  # 吨CO2/月
            "description": "月度碳排放不得超过1000吨"
        })

        # 能耗限制
        constraints.append({
            "type": "energy_consumption",
            "max_consumption": 50000,  # kWh/月
            "description": "月度能耗不得超过50000千瓦时"
        })

        return constraints

    def get_recommendations(self, data_context):
        return [
            "建议在非高峰时段安排高耗能生产任务",
            "优先使用清洁能源设备",
            "实施节能减排技术改造"
        ]
    '''

    st.code(example_code, language='python')

    # 扩展建议
    st.markdown("##### 🎯 扩展建议")

    extension_suggestions = [
        "🌱 **环保扩展**: 碳排放计算、能耗优化、废料处理",
        "💰 **成本扩展**: 全成本核算、成本分摊、盈利分析",
        "👥 **人力扩展**: 人员排班、技能匹配、培训计划",
        "🔬 **质量扩展**: 质量预测、缺陷分析、改进建议",
        "🏭 **设备扩展**: 预测性维护、设备优化、故障预警",
        "📊 **数据扩展**: 实时数据、IoT集成、大数据分析"
    ]

    for suggestion in extension_suggestions:
        st.markdown(f"- {suggestion}")

def show_logistics_config():
    """显示物流配置界面"""
    st.markdown("##### 🚚 物流扩展配置")

    # 运输配置
    with st.expander("🛣️ 运输配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**运输方式权重**")
            road_weight = st.slider("公路运输权重", 0.0, 1.0, 0.6, 0.1)
            rail_weight = st.slider("铁路运输权重", 0.0, 1.0, 0.3, 0.1)
            air_weight = st.slider("航空运输权重", 0.0, 1.0, 0.1, 0.1)

        with col2:
            st.markdown("**成本约束**")
            max_transport_cost = st.number_input("最大运输成本 (元/吨)", 50, 500, 200)
            max_lead_time = st.number_input("最大运输时间 (小时)", 1, 72, 24)
            min_capacity = st.number_input("最小运力要求 (吨/天)", 10, 200, 50)

    # 仓储配置
    with st.expander("🏭 仓储配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**容量管理**")
            safety_stock_ratio = st.slider("安全库存比例", 0.05, 0.3, 0.15, 0.05)
            max_utilization = st.slider("最大利用率", 0.7, 0.95, 0.85, 0.05)

        with col2:
            st.markdown("**成本控制**")
            storage_cost_limit = st.number_input("仓储成本限制 (元/m³/天)", 5, 50, 15)
            handling_cost_limit = st.number_input("装卸成本限制 (元/吨)", 10, 100, 25)

    # 配送配置
    with st.expander("📦 配送配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**时效要求**")
            urgent_delivery_hours = st.number_input("紧急配送时限 (小时)", 2, 24, 4)
            standard_delivery_days = st.number_input("标准配送时限 (天)", 1, 7, 2)

        with col2:
            st.markdown("**质量要求**")
            min_on_time_rate = st.slider("最低准时率", 0.8, 0.99, 0.95, 0.01)
            max_damage_rate = st.slider("最大破损率", 0.001, 0.05, 0.01, 0.001)

    # 保存配置
    if st.button("💾 保存物流配置", type="primary"):
        logistics_config = {
            "transport": {
                "road_weight": road_weight,
                "rail_weight": rail_weight,
                "air_weight": air_weight,
                "max_cost": max_transport_cost,
                "max_lead_time": max_lead_time,
                "min_capacity": min_capacity
            },
            "warehouse": {
                "safety_stock_ratio": safety_stock_ratio,
                "max_utilization": max_utilization,
                "storage_cost_limit": storage_cost_limit,
                "handling_cost_limit": handling_cost_limit
            },
            "delivery": {
                "urgent_hours": urgent_delivery_hours,
                "standard_days": standard_delivery_days,
                "min_on_time_rate": min_on_time_rate,
                "max_damage_rate": max_damage_rate
            }
        }

        # 保存配置到数据库
        try:
            from services.extension_config_service import extension_config_service
            success = extension_config_service.save_config(
                "logistics", "default", logistics_config, "user", "用户配置更新"
            )
            if success:
                st.success("✅ 物流配置已保存！")
            else:
                st.error("❌ 配置保存失败")
        except ImportError:
            st.warning("⚠️ 配置服务不可用，仅显示配置内容")
            st.success("✅ 物流配置已生成！")

        st.json(logistics_config)


def show_environmental_config():
    """显示环保配置界面"""
    st.markdown("##### 🌱 环保扩展配置")

    # 碳排放配置
    with st.expander("🌍 碳排放配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**排放限制**")
            monthly_carbon_limit = st.number_input("月度碳排放限制 (吨CO₂)", 100, 5000, 1000)
            daily_carbon_limit = st.number_input("日碳排放限制 (吨CO₂)", 1, 200, 35)

        with col2:
            st.markdown("**排放因子**")
            electricity_factor = st.number_input("电力排放因子 (kgCO₂/kWh)", 0.1, 1.0, 0.5703)
            gas_factor = st.number_input("天然气排放因子 (kgCO₂/m³)", 1.0, 3.0, 2.162)

    # 能耗配置
    with st.expander("⚡ 能耗配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**能耗限制**")
            monthly_energy_limit = st.number_input("月度能耗限制 (kWh)", 10000, 200000, 50000)
            peak_power_limit = st.number_input("峰值功率限制 (kW)", 100, 5000, 1000)

        with col2:
            st.markdown("**节能目标**")
            energy_efficiency_target = st.slider("能效提升目标", 0.05, 0.3, 0.15, 0.05)
            renewable_ratio_target = st.slider("可再生能源比例", 0.1, 0.8, 0.3, 0.1)

    # 废料处理配置
    with st.expander("♻️ 废料处理配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**废料限制**")
            max_waste_ratio = st.slider("最大废料率", 0.01, 0.1, 0.05, 0.01)
            recycling_target = st.slider("回收利用目标", 0.5, 0.95, 0.8, 0.05)

        with col2:
            st.markdown("**处理成本**")
            waste_disposal_cost = st.number_input("废料处理成本 (元/吨)", 100, 2000, 500)
            recycling_revenue = st.number_input("回收收益 (元/吨)", 50, 1000, 200)

    if st.button("💾 保存环保配置", type="primary"):
        env_config = {
            "carbon_emission": {
                "monthly_limit": monthly_carbon_limit,
                "daily_limit": daily_carbon_limit,
                "electricity_factor": electricity_factor,
                "gas_factor": gas_factor
            },
            "energy_consumption": {
                "monthly_limit": monthly_energy_limit,
                "peak_power_limit": peak_power_limit,
                "efficiency_target": energy_efficiency_target,
                "renewable_target": renewable_ratio_target
            },
            "waste_management": {
                "max_waste_ratio": max_waste_ratio,
                "recycling_target": recycling_target,
                "disposal_cost": waste_disposal_cost,
                "recycling_revenue": recycling_revenue
            }
        }

        # 保存配置到数据库
        try:
            from services.extension_config_service import extension_config_service
            success = extension_config_service.save_config(
                "environmental", "default", env_config, "user", "用户配置更新"
            )
            if success:
                st.success("✅ 环保配置已保存！")
            else:
                st.error("❌ 配置保存失败")
        except ImportError:
            st.warning("⚠️ 配置服务不可用，仅显示配置内容")
            st.success("✅ 环保配置已生成！")

        st.json(env_config)


def show_cost_config():
    """显示成本配置界面"""
    st.markdown("##### 💰 成本扩展配置")

    # 成本结构配置
    with st.expander("📊 成本结构配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**直接成本权重**")
            material_cost_weight = st.slider("原料成本权重", 0.1, 0.8, 0.4, 0.1)
            labor_cost_weight = st.slider("人工成本权重", 0.1, 0.5, 0.25, 0.05)
            energy_cost_weight = st.slider("能源成本权重", 0.05, 0.3, 0.15, 0.05)

        with col2:
            st.markdown("**间接成本权重**")
            overhead_cost_weight = st.slider("管理费用权重", 0.05, 0.2, 0.1, 0.05)
            depreciation_weight = st.slider("折旧费用权重", 0.02, 0.15, 0.08, 0.02)
            maintenance_weight = st.slider("维护费用权重", 0.02, 0.1, 0.05, 0.02)

    # 成本控制配置
    with st.expander("🎯 成本控制配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**预算控制**")
            monthly_budget = st.number_input("月度预算 (万元)", 50, 1000, 200)
            cost_variance_limit = st.slider("成本偏差限制", 0.05, 0.2, 0.1, 0.05)

        with col2:
            st.markdown("**盈利目标**")
            target_profit_margin = st.slider("目标利润率", 0.1, 0.4, 0.2, 0.05)
            min_order_profit = st.number_input("最低订单利润 (元)", 100, 10000, 1000)

    if st.button("💾 保存成本配置", type="primary"):
        cost_config = {
            "cost_structure": {
                "material_weight": material_cost_weight,
                "labor_weight": labor_cost_weight,
                "energy_weight": energy_cost_weight,
                "overhead_weight": overhead_cost_weight,
                "depreciation_weight": depreciation_weight,
                "maintenance_weight": maintenance_weight
            },
            "cost_control": {
                "monthly_budget": monthly_budget * 10000,  # 转换为元
                "variance_limit": cost_variance_limit,
                "target_profit_margin": target_profit_margin,
                "min_order_profit": min_order_profit
            }
        }

        # 保存配置到数据库
        try:
            from services.extension_config_service import extension_config_service
            success = extension_config_service.save_config(
                "cost", "default", cost_config, "user", "用户配置更新"
            )
            if success:
                st.success("✅ 成本配置已保存！")
            else:
                st.error("❌ 配置保存失败")
        except ImportError:
            st.warning("⚠️ 配置服务不可用，仅显示配置内容")
            st.success("✅ 成本配置已生成！")

        st.json(cost_config)


def show_hr_config():
    """显示人力资源配置界面"""
    st.markdown("##### 👥 人力资源扩展配置")

    # 人员配置
    with st.expander("👤 人员配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**技能等级**")
            skill_levels = st.multiselect(
                "技能等级定义",
                ["初级", "中级", "高级", "专家", "大师"],
                default=["初级", "中级", "高级"]
            )

            st.markdown("**工时配置**")
            standard_work_hours = st.number_input("标准工时 (小时/天)", 6, 12, 8)
            max_overtime_hours = st.number_input("最大加班时间 (小时/天)", 0, 6, 2)

        with col2:
            st.markdown("**人员类型**")
            worker_types = st.multiselect(
                "人员类型",
                ["操作工", "技术员", "质检员", "维修工", "班组长"],
                default=["操作工", "技术员"]
            )

            st.markdown("**成本配置**")
            base_hourly_rate = st.number_input("基础时薪 (元/小时)", 20, 200, 50)
            overtime_multiplier = st.slider("加班费倍数", 1.2, 2.0, 1.5, 0.1)

    # 排班配置
    with st.expander("📅 排班配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**班次设置**")
            shift_pattern = st.selectbox(
                "班次模式",
                ["单班制", "两班制", "三班制", "连续作业"]
            )

            shift_duration = st.number_input("班次时长 (小时)", 6, 12, 8)

        with col2:
            st.markdown("**约束条件**")
            min_rest_hours = st.number_input("最小休息时间 (小时)", 8, 24, 12)
            max_consecutive_days = st.number_input("最大连续工作天数", 3, 14, 6)

    if st.button("💾 保存人力配置", type="primary"):
        hr_config = {
            "personnel": {
                "skill_levels": skill_levels,
                "worker_types": worker_types,
                "standard_hours": standard_work_hours,
                "max_overtime": max_overtime_hours,
                "base_rate": base_hourly_rate,
                "overtime_multiplier": overtime_multiplier
            },
            "scheduling": {
                "shift_pattern": shift_pattern,
                "shift_duration": shift_duration,
                "min_rest_hours": min_rest_hours,
                "max_consecutive_days": max_consecutive_days
            }
        }

        # 保存配置到数据库
        try:
            from services.extension_config_service import extension_config_service
            success = extension_config_service.save_config(
                "hr", "default", hr_config, "user", "用户配置更新"
            )
            if success:
                st.success("✅ 人力配置已保存！")
            else:
                st.error("❌ 配置保存失败")
        except ImportError:
            st.warning("⚠️ 配置服务不可用，仅显示配置内容")
            st.success("✅ 人力配置已生成！")

        st.json(hr_config)


def show_quality_config():
    """显示质量配置界面"""
    st.markdown("##### 🔬 质量扩展配置")

    # 质量标准配置
    with st.expander("📏 质量标准配置", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**质量指标**")
            target_pass_rate = st.slider("目标合格率", 0.9, 0.999, 0.98, 0.001)
            max_defect_rate = st.slider("最大缺陷率", 0.001, 0.05, 0.01, 0.001)

        with col2:
            st.markdown("**检测配置**")
            inspection_frequency = st.selectbox(
                "检测频率",
                ["每批次", "每小时", "每班次", "每天"]
            )
            sample_size_ratio = st.slider("抽样比例", 0.01, 0.2, 0.05, 0.01)

    # 质量成本配置
    with st.expander("💸 质量成本配置"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**预防成本**")
            training_cost_ratio = st.slider("培训成本比例", 0.01, 0.1, 0.03, 0.01)
            equipment_maintenance_ratio = st.slider("设备维护比例", 0.02, 0.15, 0.05, 0.01)

        with col2:
            st.markdown("**失败成本**")
            rework_cost_multiplier = st.slider("返工成本倍数", 1.2, 3.0, 2.0, 0.1)
            scrap_cost_multiplier = st.slider("报废成本倍数", 1.5, 5.0, 3.0, 0.1)

    if st.button("💾 保存质量配置", type="primary"):
        quality_config = {
            "quality_standards": {
                "target_pass_rate": target_pass_rate,
                "max_defect_rate": max_defect_rate,
                "inspection_frequency": inspection_frequency,
                "sample_size_ratio": sample_size_ratio
            },
            "quality_costs": {
                "training_ratio": training_cost_ratio,
                "maintenance_ratio": equipment_maintenance_ratio,
                "rework_multiplier": rework_cost_multiplier,
                "scrap_multiplier": scrap_cost_multiplier
            }
        }

        # 保存配置到数据库
        try:
            from services.extension_config_service import extension_config_service
            success = extension_config_service.save_config(
                "quality", "default", quality_config, "user", "用户配置更新"
            )
            if success:
                st.success("✅ 质量配置已保存！")
            else:
                st.error("❌ 配置保存失败")
        except ImportError:
            st.warning("⚠️ 配置服务不可用，仅显示配置内容")
            st.success("✅ 质量配置已生成！")

        st.json(quality_config)


def show_template_list():
    """显示模板列表"""
    st.markdown("##### 📋 现有模板列表")

    # 选择扩展类型
    extension_type = st.selectbox(
        "选择扩展类型",
        ["logistics", "environmental", "cost", "hr", "quality"],
        format_func=lambda x: {
            "logistics": "物流扩展",
            "environmental": "环保扩展",
            "cost": "成本扩展",
            "hr": "人力扩展",
            "quality": "质量扩展"
        }[x]
    )

    # 获取模板列表
    try:
        from services.extension_config_service import extension_config_service
        templates = extension_config_service.get_template_list(extension_type)
    except ImportError:
        st.error("❌ 配置服务未找到")
        templates = []

    if templates:
        for template in templates:
            with st.expander(f"📄 {template['template_name']}" + (" (默认)" if template['is_default'] else "")):
                st.write(f"**描述**: {template['description']}")
                st.write(f"**创建时间**: {template['created_at']}")
                st.write(f"**是否默认**: {'是' if template['is_default'] else '否'}")

                # 显示模板内容
                template_data = extension_config_service.load_template(extension_type, template['template_name'])
                if template_data:
                    st.json(template_data)

                # 操作按钮
                col1, col2 = st.columns(2)
                with col1:
                    if st.button(f"📋 应用模板", key=f"apply_{template['template_name']}"):
                        # 应用模板到当前配置
                        success = extension_config_service.save_config(
                            extension_type, "default", template_data, "system", "应用模板"
                        )
                        if success:
                            st.success(f"✅ 已应用模板: {template['template_name']}")
                        else:
                            st.error("❌ 应用模板失败")

                with col2:
                    if st.button(f"📥 导出模板", key=f"export_{template['template_name']}"):
                        # 导出模板
                        st.download_button(
                            label="下载模板文件",
                            data=json.dumps(template_data, indent=2, ensure_ascii=False),
                            file_name=f"{template['template_name']}.json",
                            mime="application/json",
                            key=f"download_{template['template_name']}"
                        )
    else:
        st.info(f"暂无 {extension_type} 类型的模板")


def create_new_template():
    """创建新模板"""
    st.markdown("##### ➕ 创建新模板")

    # 基本信息
    col1, col2 = st.columns(2)

    with col1:
        extension_type = st.selectbox(
            "扩展类型",
            ["logistics", "environmental", "cost", "hr", "quality", "custom"],
            format_func=lambda x: {
                "logistics": "物流扩展",
                "environmental": "环保扩展",
                "cost": "成本扩展",
                "hr": "人力扩展",
                "quality": "质量扩展",
                "custom": "自定义扩展"
            }[x]
        )

        template_name = st.text_input("模板名称", placeholder="例如: 高成本控制模板")

    with col2:
        description = st.text_area("模板描述", placeholder="描述这个模板的用途和特点")
        is_default = st.checkbox("设为默认模板")

    # 模板内容编辑
    st.markdown("##### 📝 模板内容")

    # 提供两种方式：可视化编辑和JSON编辑
    edit_mode = st.radio("编辑模式", ["可视化编辑", "JSON编辑"])

    if edit_mode == "可视化编辑":
        if extension_type == "cost":
            template_data = create_cost_template_visual()
        elif extension_type == "logistics":
            template_data = create_logistics_template_visual()
        elif extension_type == "environmental":
            template_data = create_environmental_template_visual()
        elif extension_type == "hr":
            template_data = create_hr_template_visual()
        elif extension_type == "quality":
            template_data = create_quality_template_visual()
        else:
            template_data = create_custom_template_visual()
    else:
        # JSON编辑模式
        default_json = json.dumps({
            "example_section": {
                "parameter1": 0.5,
                "parameter2": 100,
                "parameter3": "option1"
            }
        }, indent=2, ensure_ascii=False)

        json_input = st.text_area(
            "JSON配置",
            value=default_json,
            height=300,
            help="请输入有效的JSON格式配置"
        )

        try:
            template_data = json.loads(json_input)
            st.success("✅ JSON格式正确")
        except json.JSONDecodeError as e:
            st.error(f"❌ JSON格式错误: {str(e)}")
            template_data = None

    # 预览模板
    if template_data:
        st.markdown("##### 👀 模板预览")
        st.json(template_data)

    # 保存模板
    if st.button("💾 保存模板", type="primary"):
        if template_name and template_data:
            try:
                from services.extension_config_service import extension_config_service

                # 保存为模板
                success = extension_config_service.save_template(
                    extension_type, template_name, template_data, description, is_default
                )
            except ImportError:
                st.error("❌ 配置服务未找到")
                success = False

            if success:
                st.success(f"✅ 模板 '{template_name}' 创建成功！")
                st.balloons()
            else:
                st.error("❌ 模板创建失败")
        else:
            st.warning("⚠️ 请填写模板名称和配置内容")


def create_cost_template_visual():
    """可视化创建成本模板"""
    st.markdown("**成本模板配置**")

    # 成本结构
    with st.expander("📊 成本结构", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            material_weight = st.slider("原料成本权重", 0.1, 0.8, 0.4, 0.1, key="tmpl_material")
            labor_weight = st.slider("人工成本权重", 0.1, 0.5, 0.25, 0.05, key="tmpl_labor")
            energy_weight = st.slider("能源成本权重", 0.05, 0.3, 0.15, 0.05, key="tmpl_energy")

        with col2:
            overhead_weight = st.slider("管理费用权重", 0.05, 0.2, 0.1, 0.05, key="tmpl_overhead")
            depreciation_weight = st.slider("折旧费用权重", 0.02, 0.15, 0.08, 0.02, key="tmpl_depreciation")
            maintenance_weight = st.slider("维护费用权重", 0.02, 0.1, 0.05, 0.02, key="tmpl_maintenance")

    # 成本控制
    with st.expander("🎯 成本控制"):
        col1, col2 = st.columns(2)

        with col1:
            monthly_budget = st.number_input("月度预算 (万元)", 50, 1000, 200, key="tmpl_budget")
            variance_limit = st.slider("成本偏差限制", 0.05, 0.2, 0.1, 0.05, key="tmpl_variance")

        with col2:
            profit_margin = st.slider("目标利润率", 0.1, 0.4, 0.2, 0.05, key="tmpl_profit")
            min_order_profit = st.number_input("最低订单利润 (元)", 100, 10000, 1000, key="tmpl_min_profit")

    return {
        "cost_structure": {
            "material_weight": material_weight,
            "labor_weight": labor_weight,
            "energy_weight": energy_weight,
            "overhead_weight": overhead_weight,
            "depreciation_weight": depreciation_weight,
            "maintenance_weight": maintenance_weight
        },
        "cost_control": {
            "monthly_budget": monthly_budget * 10000,
            "variance_limit": variance_limit,
            "target_profit_margin": profit_margin,
            "min_order_profit": min_order_profit
        }
    }


def create_logistics_template_visual():
    """可视化创建物流模板"""
    st.markdown("**物流模板配置**")

    # 简化的物流配置
    road_weight = st.slider("公路运输权重", 0.0, 1.0, 0.6, 0.1, key="tmpl_road")
    rail_weight = st.slider("铁路运输权重", 0.0, 1.0, 0.3, 0.1, key="tmpl_rail")
    max_cost = st.number_input("最大运输成本 (元/吨)", 50, 500, 200, key="tmpl_max_cost")

    return {
        "transport": {
            "road_weight": road_weight,
            "rail_weight": rail_weight,
            "air_weight": 1.0 - road_weight - rail_weight,
            "max_cost": max_cost,
            "max_lead_time": 24,
            "min_capacity": 50
        }
    }


def create_environmental_template_visual():
    """可视化创建环保模板"""
    st.markdown("**环保模板配置**")

    carbon_limit = st.number_input("月度碳排放限制 (吨CO₂)", 100, 5000, 1000, key="tmpl_carbon")
    energy_limit = st.number_input("月度能耗限制 (kWh)", 10000, 200000, 50000, key="tmpl_energy_limit")

    return {
        "carbon_emission": {
            "monthly_limit": carbon_limit,
            "daily_limit": carbon_limit / 30,
            "electricity_factor": 0.5703
        },
        "energy_consumption": {
            "monthly_limit": energy_limit,
            "efficiency_target": 0.15
        }
    }


def create_hr_template_visual():
    """可视化创建人力模板"""
    st.markdown("**人力模板配置**")

    standard_hours = st.number_input("标准工时 (小时/天)", 6, 12, 8, key="tmpl_hours")
    base_rate = st.number_input("基础时薪 (元/小时)", 20, 200, 50, key="tmpl_rate")

    return {
        "personnel": {
            "standard_hours": standard_hours,
            "base_rate": base_rate,
            "overtime_multiplier": 1.5
        }
    }


def create_quality_template_visual():
    """可视化创建质量模板"""
    st.markdown("**质量模板配置**")

    pass_rate = st.slider("目标合格率", 0.9, 0.999, 0.98, 0.001, key="tmpl_pass_rate")
    defect_rate = st.slider("最大缺陷率", 0.001, 0.05, 0.01, 0.001, key="tmpl_defect_rate")

    return {
        "quality_standards": {
            "target_pass_rate": pass_rate,
            "max_defect_rate": defect_rate
        }
    }


def create_custom_template_visual():
    """可视化创建自定义模板"""
    st.markdown("**自定义模板配置**")
    st.info("💡 自定义扩展请使用JSON编辑模式")

    return {
        "custom_section": {
            "parameter1": 0.5,
            "parameter2": 100
        }
    }


def edit_existing_template():
    """编辑现有模板"""
    st.markdown("##### ✏️ 编辑现有模板")
    st.info("🚧 编辑功能开发中，请先删除旧模板再创建新模板")


def delete_template():
    """删除模板"""
    st.markdown("##### 🗑️ 删除模板")
    st.warning("⚠️ 删除操作不可恢复，请谨慎操作")
    st.info("🚧 删除功能开发中")


# 页面底部信息
st.markdown("---")
st.markdown("💡 **提示**: 规划引擎扩展功能支持无限扩展，您可以根据业务需求添加任何类型的规划因素和约束条件。")
