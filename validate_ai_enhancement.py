#!/usr/bin/env python3
"""
AI能力增强功能验证脚本
简化版本，用于快速验证核心功能
"""

import sys
import os

def check_file_structure():
    """检查文件结构"""
    print("🔍 检查文件结构...")
    
    required_files = [
        "frontend/services/ai_enhancement_service.py",
        "frontend/pages/12_AI能力增强.py", 
        "frontend/config/ai_enhancement_config.py",
        "backend/routers/ai_enhancement.py",
        "tests/test_ai_enhancement.py",
        "docs/AI_ENHANCEMENT_GUIDE.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def check_imports():
    """检查关键导入"""
    print("\n🔍 检查关键导入...")
    
    try:
        # 添加路径
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        # 检查核心模块
        import pandas as pd
        import numpy as np
        print("✅ pandas, numpy 导入成功")
        
        from sklearn.ensemble import IsolationForest, RandomForestRegressor
        print("✅ scikit-learn 导入成功")
        
        # 检查AI服务模块
        from services.ai_enhancement_service import (
            AIEnhancementService,
            PredictiveAnalyticsEngine,
            AnomalyDetectionEngine,
            IntelligentOptimizationEngine
        )
        print("✅ AI增强服务模块导入成功")
        
        # 检查枚举类型
        from services.ai_enhancement_service import (
            PredictionType,
            AnomalyType,
            OptimizationType
        )
        print("✅ 枚举类型导入成功")
        
        # 检查配置模块
        from config.ai_enhancement_config import (
            PREDICTION_CONFIGS,
            ANOMALY_DETECTION_CONFIGS,
            OPTIMIZATION_CONFIGS
        )
        print("✅ 配置模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def check_service_initialization():
    """检查服务初始化"""
    print("\n🔍 检查服务初始化...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        from services.ai_enhancement_service import AIEnhancementService
        
        # 创建服务实例
        service = AIEnhancementService()
        
        # 检查子服务
        assert hasattr(service, 'predictive_analytics')
        assert hasattr(service, 'anomaly_detection')
        assert hasattr(service, 'intelligent_optimization')
        
        print("✅ AI增强服务初始化成功")
        
        # 检查服务状态
        assert hasattr(service, 'service_status')
        assert len(service.service_status) == 3
        
        print("✅ 服务状态检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False

def check_api_integration():
    """检查API集成"""
    print("\n🔍 检查API集成...")
    
    try:
        # 检查后端路由文件
        backend_router_path = "backend/routers/ai_enhancement.py"
        if not os.path.exists(backend_router_path):
            print(f"❌ 后端路由文件不存在: {backend_router_path}")
            return False
        
        # 检查API路由集成
        api_file_path = "backend/app/api/v1/api.py"
        if not os.path.exists(api_file_path):
            print(f"❌ API文件不存在: {api_file_path}")
            return False
        
        with open(api_file_path, 'r', encoding='utf-8') as f:
            api_content = f.read()
        
        # 检查是否包含AI增强路由
        if "ai_enhancement" not in api_content:
            print("❌ API文件中未包含AI增强路由")
            return False
        
        if "AI能力增强" not in api_content:
            print("❌ API文件中未包含AI增强标签")
            return False
        
        print("✅ API集成检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ API集成检查失败: {e}")
        return False

def check_frontend_integration():
    """检查前端集成"""
    print("\n🔍 检查前端集成...")
    
    try:
        # 检查主页面是否包含AI增强导航
        main_file_path = "frontend/main.py"
        if not os.path.exists(main_file_path):
            print(f"❌ 主页面文件不存在: {main_file_path}")
            return False
        
        with open(main_file_path, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # 检查是否包含AI能力增强导航
        if "AI能力增强" not in main_content:
            print("❌ 主页面中未包含AI能力增强导航")
            return False
        
        if "12_AI能力增强.py" not in main_content:
            print("❌ 主页面中未包含AI增强页面链接")
            return False
        
        print("✅ 前端集成检查通过")
        
        # 检查AI增强页面
        ai_page_path = "frontend/pages/12_AI能力增强.py"
        if not os.path.exists(ai_page_path):
            print(f"❌ AI增强页面不存在: {ai_page_path}")
            return False
        
        with open(ai_page_path, 'r', encoding='utf-8') as f:
            page_content = f.read()
        
        # 检查页面关键组件
        required_components = [
            "预测分析",
            "异常检测", 
            "智能优化",
            "综合分析",
            "streamlit"
        ]
        
        for component in required_components:
            if component not in page_content:
                print(f"❌ AI增强页面缺少组件: {component}")
                return False
        
        print("✅ AI增强页面检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端集成检查失败: {e}")
        return False

def check_configuration():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        from config.ai_enhancement_config import (
            PREDICTION_CONFIGS,
            ANOMALY_DETECTION_CONFIGS,
            OPTIMIZATION_CONFIGS,
            AI_SERVICE_CONFIG,
            PERFORMANCE_BENCHMARKS
        )
        
        # 检查预测配置
        assert "demand_forecast" in PREDICTION_CONFIGS
        assert "equipment_failure" in PREDICTION_CONFIGS
        print("✅ 预测配置检查通过")
        
        # 检查异常检测配置
        assert "production_monitoring" in ANOMALY_DETECTION_CONFIGS
        print("✅ 异常检测配置检查通过")
        
        # 检查优化配置
        assert "production_scheduling" in OPTIMIZATION_CONFIGS
        assert "energy_optimization" in OPTIMIZATION_CONFIGS
        print("✅ 优化配置检查通过")
        
        # 检查服务配置
        assert "general" in AI_SERVICE_CONFIG
        assert "data_processing" in AI_SERVICE_CONFIG
        print("✅ 服务配置检查通过")
        
        # 检查性能基准
        assert "prediction_accuracy" in PERFORMANCE_BENCHMARKS
        assert "anomaly_detection" in PERFORMANCE_BENCHMARKS
        print("✅ 性能基准配置检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始AI能力增强功能验证")
    print("=" * 50)
    
    checks = [
        ("文件结构", check_file_structure),
        ("关键导入", check_imports),
        ("服务初始化", check_service_initialization),
        ("API集成", check_api_integration),
        ("前端集成", check_frontend_integration),
        ("配置文件", check_configuration)
    ]
    
    passed = 0
    failed = 0
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            if result:
                passed += 1
                print(f"✅ {check_name} 验证通过")
            else:
                failed += 1
                print(f"❌ {check_name} 验证失败")
        except Exception as e:
            failed += 1
            print(f"❌ {check_name} 验证异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证结果汇总:")
    print(f"✅ 通过: {passed} 项")
    print(f"❌ 失败: {failed} 项")
    print(f"📊 总计: {passed + failed} 项")
    
    if failed == 0:
        print("\n🎉 所有验证通过！AI能力增强功能集成正常。")
        print("\n📝 功能特性:")
        print("  • 📈 预测分析: 需求预测、设备故障预测、质量预测")
        print("  • 🔍 异常检测: 统计检测、孤立森林、集成检测")
        print("  • ⚡ 智能优化: 生产排程、资源分配、能耗优化")
        print("  • 📊 综合分析: 一键执行全面AI分析")
        print("  • 🔧 配置管理: 灵活的参数配置和性能监控")
        print("  • 🌐 API接口: 完整的RESTful API支持")
        print("  • 🖥️ 前端界面: 交互式Streamlit界面")
        
        return True
    else:
        print(f"\n⚠️ 有 {failed} 项验证失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
