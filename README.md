# 🏭 Smart APS - 智能工厂生产管理规划系统

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.28+-red.svg)](https://streamlit.io)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 专为20-30人小规模团队设计的智能工厂生产管理规划系统

## 📋 项目简介

Smart APS（Advanced Planning and Scheduling）是一个现代化的生产调度优化平台，通过整合传统优化算法、实时数据处理和本地/云端LLM的推理能力，实现智能化的生产规划和排程。

### 🎯 核心特性

- **🤖 智能化决策**: 结合MILP优化算法与LLM推理，提供更准确的生产规划
- **📁 灵活数据源**: 支持邮件、Excel文件上传和自动数据提取
- **📊 交互式前端**: 基于Streamlit的丰富图表和用户交互界面
- **⚙️ 灵活配置**: 高度可配置的数据源、约束条件和业务规则
- **🔒 本地部署**: 支持Ollama本地LLM和Azure OpenAI云端服务

### 📈 预期效益

| 指标 | 预期提升 | 说明 |
|------|---------|------|
| 生产效率 | 15%-20% | 优化资源配置，减少设备空闲时间 |
| 计划准确率 | 提升至90%+ | 智能算法结合历史学习 |
| 响应速度 | 从小时级到分钟级 | 自动化计划生成和调整 |
| 人工工作量 | 减少50% | 自动化数据处理和计划生成 |

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│              前端展示层 (Streamlit + Plotly)                   │
├─────────────────────────────────────────────────────────────┤
│                    API服务层 (FastAPI)                        │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 文件上传     │ │ 数据源集成   │ │ 规划引擎     │ │ 算法模块     │ │
│  │ 邮件解析     │ │ Excel解析    │ │ 约束管理     │ │ MILP优化    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ LLM集成     │ │ 权限管理     │ │ 插件管理     │ │ 配置管理     │ │
│  │ Ollama/Azure│ │ 用户认证     │ │ 扩展支持     │ │ 灵活配置     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (SQLAlchemy)                     │
├─────────────────────────────────────────────────────────────┤
│              数据存储层 (MySQL + Redis)                        │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **Python 3.11+** - 主要开发语言
- **FastAPI** - 高性能Web框架
- **SQLAlchemy** - ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 7.0+** - 缓存和会话管理

### 前端技术
- **Streamlit** - 快速构建数据应用
- **Plotly + Altair** - 丰富的交互式图表
- **Streamlit-aggrid** - 高级表格组件

### 算法与AI
- **PuLP** - 线性规划求解器
- **Scikit-learn** - 机器学习
- **Ollama** - 本地大型语言模型
- **Azure OpenAI** - 云端LLM服务

### 文件处理
- **openpyxl** - Excel文件处理
- **email-parser** - 邮件解析
- **python-multipart** - 文件上传

## 🚀 快速开始

### 环境要求

- **Python 3.11+**
- **Docker 20.10+** 和 **Docker Compose**
- **4核CPU, 8GB内存** (推荐配置)

### 一键部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/smart-aps.git
cd smart-aps

# 2. 复制环境配置
cp backend/.env.example backend/.env

# 3. 启动所有服务
docker-compose up -d

# 4. 等待服务启动完成（约2-3分钟）
docker-compose logs -f

# 访问地址：
# - 前端界面: http://localhost:8501
# - API文档: http://localhost:8000/docs
# - 默认账户: admin / admin123456
```

### 本地开发部署

```bash
# 1. 启动数据库服务
docker-compose up -d mysql redis

# 2. 安装后端依赖
cd backend
pip install -r requirements.txt

# 3. 初始化数据库
python scripts/init_database.py
python scripts/init_data.py

# 4. 启动后端API
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 5. 安装前端依赖
cd ../frontend
pip install -r requirements.txt

# 6. 启动前端应用
streamlit run main.py
```

### Ollama本地LLM配置（可选）

```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载模型
ollama pull llama2
ollama pull mistral

# 3. 启动Ollama服务
ollama serve

# 4. 配置环境变量
export OLLAMA_BASE_URL=http://localhost:11434
export DEFAULT_LLM_SERVICE=ollama
```

## 📚 文档

详细文档请查看 [Doc](./Doc/) 目录：

- [项目概览与架构](./Doc/01_项目概览与架构.md)
- [需求分析与用户指南](./Doc/02_需求分析与用户指南.md)
- [技术设计文档](./Doc/03_技术设计文档.md)
- [开发实施指南](./Doc/04_开发实施指南.md)
- [扩展与插件开发](./Doc/05_扩展与插件开发.md)
- [技术评估与最佳实践](./Doc/06_技术评估与最佳实践.md)
- [Streamlit前端开发指南](./Doc/07_Streamlit前端开发指南.md)

## 🎮 功能演示

### 1. 智能文件上传
- 支持拖拽上传Excel、邮件文件
- 自动识别和提取表格数据
- 可视化字段映射配置

### 2. 生产规划优化
- MILP算法优化资源配置
- 交互式甘特图展示
- 实时约束条件调整

### 3. AI智能助手
- 自然语言查询生产数据
- 智能分析和建议
- 支持本地和云端LLM

### 4. 数据可视化
- 丰富的交互式图表
- 实时数据监控
- 自定义仪表盘

## 🔧 配置说明

### 环境变量配置

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=smart_aps
DB_PASSWORD=smart_aps_password
DB_NAME=smart_aps

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM配置
DEFAULT_LLM_SERVICE=ollama  # ollama 或 azure
OLLAMA_BASE_URL=http://localhost:11434
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
```

### Docker Compose配置

系统提供完整的Docker Compose配置，包括：
- MySQL 8.0 数据库
- Redis 7.0 缓存
- FastAPI 后端服务
- Streamlit 前端应用
- Ollama LLM服务（可选）

## 🧪 测试

```bash
# 后端测试
cd backend
pytest tests/ -v

# 前端测试
cd frontend
pytest tests/ -v

# 集成测试
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 性能指标

- **响应时间**: API响应 < 200ms
- **并发用户**: 支持20-30并发用户
- **文件处理**: 支持200MB文件上传
- **算法性能**: MILP求解 < 30分钟

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目主页**: [GitHub](https://github.com/your-org/smart-aps)
- **问题反馈**: [Issues](https://github.com/your-org/smart-aps/issues)
- **技术支持**: <EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- [FastAPI](https://fastapi.tiangolo.com/)
- [Streamlit](https://streamlit.io/)
- [Plotly](https://plotly.com/)
- [SQLAlchemy](https://www.sqlalchemy.org/)
- [Ollama](https://ollama.ai/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
