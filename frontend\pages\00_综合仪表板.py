"""
Smart APS 综合仪表板
集成各种图表和组件的数据可视化中心
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入组件
from components.production_charts import (
    render_production_chart_widget,
    render_production_summary_widget
)

# 页面标题
st.title("🏭 Smart APS 智能生产管理系统")
st.markdown("### 📊 生产管理综合仪表板")

# 侧边栏 - 系统状态和快速操作
with st.sidebar:
    st.markdown("### 🔧 系统状态")

    # 系统状态指标
    col1, col2 = st.columns(2)

    with col1:
        st.metric("系统状态", "正常", delta="在线")

    with col2:
        st.metric("在线用户", "12", delta="+2")

    st.markdown("---")

    # LLM状态检查
    st.markdown("### 🤖 AI助手状态")

    # 从配置中读取LLM启用状态（模拟）
    llm_enabled = st.checkbox("启用LLM智能助手", value=True, help="控制是否启用AI智能助手功能")

    if llm_enabled:
        llm_provider = st.selectbox(
            "LLM服务商",
            options=["Ollama本地", "Azure OpenAI"],
            index=0
        )

        if llm_provider == "Ollama本地":
            st.success("✅ Ollama服务正常")
            st.info("模型: qwen2.5:7b")
        else:
            st.success("✅ Azure OpenAI连接正常")
            st.info("模型: gpt-3.5-turbo")
    else:
        st.warning("⚠️ LLM功能已禁用")
        st.info("💡 智能助手将显示基础功能")

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("📁 数据上传", use_container_width=True):
        st.switch_page("pages/01_数据上传.py")

    if st.button("📊 生产规划", use_container_width=True):
        st.switch_page("pages/02_生产规划.py")

    if st.button("⚙️ 设备管理", use_container_width=True):
        st.switch_page("pages/03_设备管理.py")

    if st.button("📈 计划监控", use_container_width=True):
        st.switch_page("pages/04_计划监控.py")

    if st.button("🤖 智能助手", use_container_width=True):
        st.switch_page("pages/06_智能助手.py")

    if st.button("🏠 返回主页", use_container_width=True, type="secondary"):
        st.switch_page("main.py")

# 主要内容区域
# 第一行 - 生产概览
st.markdown("#### 📈 生产概览")
render_production_summary_widget()

st.markdown("---")

# 第二行 - 生产计划可视化
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("#### 📊 生产计划可视化")

    # 图表类型选择
    chart_type = st.selectbox(
        "选择图表类型",
        options=["甘特图", "时间线图", "负载图", "资源分配图", "进度仪表板"],
        index=0,
        key="dashboard_chart_type"
    )

    # 渲染选择的图表
    render_production_chart_widget(chart_type=chart_type, height=400, show_controls=False)

with col2:
    st.markdown("#### 🎯 关键指标")

    # 关键指标卡片
    st.metric("生产效率", "85.5%", delta="↑5.2%")
    st.metric("设备利用率", "78.3%", delta="↑2.1%")
    st.metric("质量合格率", "96.8%", delta="↑0.5%")
    st.metric("按时交付率", "92.3%", delta="↓1.2%")

    st.markdown("---")

    # 今日任务
    st.markdown("##### 📋 今日任务")

    today_tasks = [
        {"task": "订单A生产", "status": "进行中", "progress": 75},
        {"task": "设备B维护", "status": "待开始", "progress": 0},
        {"task": "质量检查", "status": "已完成", "progress": 100}
    ]

    for task in today_tasks:
        status_color = {
            "进行中": "🟡",
            "待开始": "⚪",
            "已完成": "🟢"
        }

        st.write(f"{status_color[task['status']]} {task['task']}")
        st.progress(task['progress'] / 100)

st.markdown("---")

# 第三行 - 算法学习状态和系统配置
col1, col2 = st.columns(2)

with col1:
    st.markdown("#### 🧠 算法学习状态")

    # 算法学习配置状态（模拟）
    algorithm_learning_enabled = st.checkbox(
        "启用算法学习",
        value=True,
        help="是否启用生产计划算法的自动学习优化"
    )

    if algorithm_learning_enabled:
        st.success("✅ 算法学习已启用")

        # 学习进度
        col1_1, col1_2 = st.columns(2)

        with col1_1:
            st.metric("学习样本数", "156", delta="+12")
            st.metric("模型准确率", "94.2%", delta="↑2.3%")

        with col1_2:
            st.metric("优化轮次", "23", delta="+1")
            st.metric("性能提升", "15.8%", delta="↑1.2%")

        # 学习趋势图
        import plotly.express as px

        dates = pd.date_range(start="2024-01-01", periods=30, freq="D")
        accuracy = [85 + i * 0.3 + (i % 7) * 0.5 for i in range(30)]

        fig_learning = px.line(
            x=dates,
            y=accuracy,
            title="算法学习准确率趋势",
            markers=True
        )

        fig_learning.update_layout(
            height=300,
            margin=dict(l=20, r=20, t=40, b=20),
            xaxis_title="日期",
            yaxis_title="准确率 (%)"
        )

        st.plotly_chart(fig_learning, use_container_width=True)

        # 学习参数调整
        with st.expander("🔧 学习参数调整"):
            learning_rate = st.slider("学习率", 0.01, 0.5, 0.1, 0.01)
            batch_size = st.selectbox("批次大小", [16, 32, 64, 128], index=1)

            if st.button("💾 保存学习参数"):
                st.success("学习参数已保存")

    else:
        st.warning("⚠️ 算法学习已禁用")
        st.info("💡 系统将使用固定的优化参数")

with col2:
    st.markdown("#### ⚙️ 系统配置状态")

    # 配置状态概览
    config_status = {
        "系统参数": "✅ 正常",
        "业务配置": "✅ 正常",
        "集成配置": "⚠️ 部分异常",
        "界面配置": "✅ 正常",
        "监控配置": "✅ 正常"
    }

    for config_type, status in config_status.items():
        st.write(f"**{config_type}**: {status}")

    st.markdown("---")

    # 最近配置变更
    st.markdown("##### 📝 最近配置变更")

    recent_changes = [
        {"time": "10:30", "type": "业务配置", "action": "更新质量目标"},
        {"time": "09:15", "type": "系统参数", "action": "调整并发用户数"},
        {"time": "昨天", "type": "集成配置", "action": "添加ERP连接"}
    ]

    for change in recent_changes:
        st.write(f"**{change['time']}** - {change['type']}: {change['action']}")

    st.markdown("---")

    # 配置快速操作
    if st.button("🔧 系统配置", use_container_width=True):
        st.switch_page("pages/08_系统配置.py")

    if st.button("📊 性能监控", use_container_width=True):
        st.info("跳转到监控页面...")

st.markdown("---")

# 第四行 - 多图表展示区域
st.markdown("#### 📊 多维度分析")

# 创建标签页展示不同类型的图表
tab1, tab2, tab3, tab4 = st.tabs(["📈 趋势分析", "🔧 设备状态", "📋 订单分析", "🎯 质量监控"])

with tab1:
    col1, col2 = st.columns(2)

    with col1:
        render_production_chart_widget("时间线图", height=350, show_controls=False)

    with col2:
        render_production_chart_widget("负载图", height=350, show_controls=False)

with tab2:
    col1, col2 = st.columns(2)

    with col1:
        render_production_chart_widget("资源分配图", height=350, show_controls=False)

    with col2:
        # 设备状态概览
        st.markdown("##### ⚙️ 设备状态概览")

        equipment_status = [
            {"name": "设备1", "status": "运行中", "utilization": 85, "oee": 82},
            {"name": "设备2", "status": "运行中", "utilization": 92, "oee": 88},
            {"name": "设备3", "status": "维护中", "utilization": 0, "oee": 0},
            {"name": "设备4", "status": "待机", "utilization": 15, "oee": 12}
        ]

        for eq in equipment_status:
            status_color = {
                "运行中": "🟢",
                "维护中": "🔴",
                "待机": "🟡",
                "离线": "⚫"
            }

            st.write(f"{status_color[eq['status']]} **{eq['name']}** - {eq['status']}")

            col_util, col_oee = st.columns(2)
            with col_util:
                st.metric("利用率", f"{eq['utilization']}%")
            with col_oee:
                st.metric("OEE", f"{eq['oee']}%")

            st.markdown("---")

with tab3:
    render_production_chart_widget("甘特图", height=400, show_controls=False)

with tab4:
    render_production_chart_widget("进度仪表板", height=400, show_controls=False)

# 页面底部 - 系统信息
st.markdown("---")
st.markdown("##### 📋 系统信息")

col1, col2, col3, col4 = st.columns(4)

with col1:
    st.write("**系统版本**: Smart APS v1.0.0")

with col2:
    st.write("**最后更新**: 2024-01-16 10:30:00")

with col3:
    st.write("**数据库状态**: 正常连接")

with col4:
    st.write("**API状态**: 服务正常")