# Smart APS 数据源扩展指南

## 📋 概述

Smart APS 数据集成系统采用插件化架构，支持灵活添加新的数据源。系统可以集成各种类型的数据源，包括数据库、API、文件、传感器等，为LLM和算法提供完整的数据上下文。

## 🏗️ 系统架构

### 核心组件

1. **DataIntegrationService** - 数据集成服务核心
2. **DataSourcePlugin** - 数据源插件基类
3. **数据源管理页面** - 可视化管理界面

### 支持的数据源类型

- 🏗️ **核心数据源** - 系统内置（设备、PCI、上传文件等）
- 🔌 **插件数据源** - 自定义插件
- 🗄️ **数据库** - MySQL、PostgreSQL、SQL Server等
- 🌐 **API接口** - REST API、GraphQL等
- 📁 **文件数据源** - CSV、Excel、JSON等
- 📡 **传感器数据** - IoT设备、MQTT等

## 🔌 创建自定义数据源插件

### 1. 继承DataSourcePlugin基类

```python
from services.data_integration_service import DataSourcePlugin

class MyCustomPlugin(DataSourcePlugin):
    def __init__(self, config: Dict[str, Any]):
        super().__init__("my_custom_source", "我的自定义数据源")
        self.config = config
    
    def get_data_context(self) -> Dict[str, Any]:
        """获取数据上下文 - 必须实现"""
        return {
            "data": self._fetch_data(),
            "status": "connected",
            "last_update": datetime.now().isoformat()
        }
    
    def get_constraints(self) -> List[Dict[str, Any]]:
        """获取约束条件 - 可选实现"""
        return []
    
    def get_recommendations(self) -> List[str]:
        """获取建议 - 可选实现"""
        return []
    
    def _fetch_data(self):
        """获取实际数据"""
        # 实现数据获取逻辑
        pass
```

### 2. 注册插件

```python
# 创建插件实例
my_plugin = MyCustomPlugin(config={
    "endpoint": "http://api.example.com",
    "api_key": "your_key"
})

# 注册到数据集成服务
data_integration_service.register_data_source_plugin(my_plugin)
```

## 📊 添加自定义数据源

### 1. 通过代码添加

```python
data_integration_service.add_custom_data_source(
    source_name="external_erp",
    source_type="api",
    config={
        "endpoint": "http://erp.company.com/api/v1",
        "api_key": "your_api_key",
        "timeout": 30
    },
    enabled=True
)
```

### 2. 通过管理界面添加

1. 访问 "数据源管理" 页面
2. 点击 "添加数据源" 标签
3. 选择数据源类型
4. 填写配置信息
5. 点击 "添加数据源"

## 🔧 数据源配置示例

### 数据库数据源

```python
database_config = {
    "connection": {
        "type": "mysql",
        "host": "localhost",
        "port": 3306,
        "database": "production_db",
        "username": "user",
        "password": "password"
    },
    "tables": ["orders", "inventory", "production"]
}
```

### API数据源

```python
api_config = {
    "endpoint": "https://api.example.com/v1/data",
    "api_key": "your_api_key",
    "headers": {
        "Content-Type": "application/json"
    },
    "timeout": 30
}
```

### 文件数据源

```python
file_config = {
    "file_path": "/data/production/daily_report.csv",
    "format": "csv",
    "encoding": "utf-8",
    "update_frequency": "daily"
}
```

### 传感器数据源

```python
sensor_config = {
    "protocol": "MQTT",
    "address": "*************",
    "port": 1883,
    "sensors": ["TEMP001", "PRES001", "VIBR001"]
}
```

## 🤖 LLM集成

### 数据上下文格式

数据源提供的数据会自动集成到LLM上下文中：

```python
# 获取完整数据上下文
context = data_integration_service.get_comprehensive_data_context()

# LLM上下文包含：
{
    "timestamp": "2024-01-16T10:30:00",
    "data_sources": {
        "equipment": {...},
        "pci": {...},
        "my_custom_source": {...}  # 自定义数据源
    },
    "summary": {...},
    "constraints": [...],
    "recommendations": [...]
}
```

### LLM提示上下文

```python
# 获取格式化的LLM提示
llm_prompt = data_integration_service.get_llm_prompt_context()

# 示例输出：
"""
当前系统数据概览（2024-01-16T10:30:00）：

设备状态：
- 总设备数：9台
- 可用设备：6台
- 不可用设备：3台

自定义数据源：
- ERP订单：15个待处理
- 传感器状态：正常
- 质量数据：合格率98.5%

约束条件：
- L02: 原料短缺
- 传感器VIBR001检测到异常

建议：
- 优先处理高优先级订单
- 检查设备振动异常
"""
```

## 🧮 算法集成

### 算法输入数据格式

```python
# 获取算法输入数据
algorithm_data = data_integration_service.get_algorithm_input_data()

# 数据结构：
{
    "equipment": {
        "available_capacity": {...},
        "constraints": [...]
    },
    "materials": {
        "fs_inventory": [...],
        "consumption_priorities": [...]
    },
    "custom_sources": {
        "erp_orders": [...],
        "sensor_data": [...],
        "quality_metrics": [...]
    },
    "constraints": [...],
    "optimization_targets": {...}
}
```

## 📈 扩展示例

### 1. ERP系统集成

```python
class ERPIntegration(DataSourcePlugin):
    def get_data_context(self):
        return {
            "orders": self._fetch_orders(),
            "inventory": self._fetch_inventory(),
            "customers": self._fetch_customers()
        }
    
    def get_constraints(self):
        # 检查库存约束
        constraints = []
        inventory = self._fetch_inventory()
        for item in inventory:
            if item["stock"] < item["safety_stock"]:
                constraints.append({
                    "type": "inventory_shortage",
                    "description": f"原料{item['name']}库存不足"
                })
        return constraints
```

### 2. IoT传感器集成

```python
class IoTSensorIntegration(DataSourcePlugin):
    def get_data_context(self):
        return {
            "temperature_sensors": self._read_temperature(),
            "pressure_sensors": self._read_pressure(),
            "vibration_sensors": self._read_vibration()
        }
    
    def get_constraints(self):
        constraints = []
        sensors = self._read_all_sensors()
        for sensor in sensors:
            if sensor["status"] == "warning":
                constraints.append({
                    "type": "sensor_warning",
                    "description": f"传感器{sensor['id']}异常"
                })
        return constraints
```

### 3. 质量管理系统集成

```python
class QualitySystemIntegration(DataSourcePlugin):
    def get_data_context(self):
        return {
            "quality_metrics": self._get_quality_stats(),
            "test_results": self._get_recent_tests(),
            "defect_analysis": self._analyze_defects()
        }
    
    def get_recommendations(self):
        recommendations = []
        quality_rate = self._get_quality_rate()
        if quality_rate < 95:
            recommendations.append("质量合格率偏低，建议检查生产工艺")
        return recommendations
```

## 🔄 数据同步和缓存

### 同步策略

- **实时同步** - 适用于关键数据源
- **定时同步** - 适用于变化较慢的数据
- **事件驱动** - 基于数据变化触发

### 缓存机制

- **内存缓存** - 快速访问频繁使用的数据
- **持久化缓存** - 减少外部系统调用
- **智能失效** - 基于数据变化自动更新

## 🛠️ 最佳实践

### 1. 错误处理

```python
def get_data_context(self):
    try:
        return self._fetch_data()
    except Exception as e:
        return {
            "error": str(e),
            "status": "error",
            "last_update": datetime.now().isoformat()
        }
```

### 2. 性能优化

- 使用连接池
- 实现数据分页
- 添加超时控制
- 启用数据压缩

### 3. 安全考虑

- 加密敏感配置
- 使用安全的认证方式
- 实现访问控制
- 记录审计日志

## 🚀 未来扩展方向

1. **实时流数据** - 支持Kafka、Redis Stream等
2. **云服务集成** - AWS、Azure、阿里云等
3. **机器学习平台** - MLflow、Kubeflow等
4. **区块链数据** - 供应链追溯等
5. **边缘计算** - 边缘设备数据采集

## 📞 技术支持

如需技术支持或有扩展需求，请联系开发团队。
