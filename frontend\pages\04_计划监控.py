"""
计划监控页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import time

from config.settings import AppConfig
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="计划监控 - Smart APS",
    page_icon="📈",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("monitoring.view"):
    st.error("权限不足")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 初始化会话状态
if 'auto_refresh' not in st.session_state:
    st.session_state.auto_refresh = True

if 'refresh_interval' not in st.session_state:
    st.session_state.refresh_interval = 30

if 'selected_alerts' not in st.session_state:
    st.session_state.selected_alerts = []

# 页面标题
st.title("📈 计划监控")
st.markdown("### 实时监控生产计划执行情况，及时发现和处理异常")

# 侧边栏 - 监控配置
with st.sidebar:
    st.markdown("### ⚙️ 监控配置")

    # 自动刷新设置
    auto_refresh = st.checkbox("自动刷新", value=st.session_state.auto_refresh)
    st.session_state.auto_refresh = auto_refresh

    if auto_refresh:
        refresh_interval = st.selectbox(
            "刷新间隔",
            [10, 30, 60, 120, 300],
            index=1,
            format_func=lambda x: f"{x}秒"
        )
        st.session_state.refresh_interval = refresh_interval

    # 监控范围
    st.markdown("#### 📊 监控范围")

    monitor_scope = st.selectbox(
        "监控范围",
        ["全部", "当前班次", "今日", "本周", "自定义"]
    )

    if monitor_scope == "自定义":
        start_date = st.date_input("开始日期", value=datetime.now().date())
        end_date = st.date_input("结束日期", value=datetime.now().date())
        # 存储自定义日期范围到会话状态
        st.session_state.custom_date_range = (start_date, end_date)

    # 告警级别筛选
    alert_levels = st.multiselect(
        "告警级别",
        ["严重", "警告", "信息"],
        default=["严重", "警告"]
    )

    # 监控指标选择
    st.markdown("#### 📈 监控指标")

    selected_metrics = st.multiselect(
        "选择指标",
        ["设备利用率", "生产效率", "质量指标", "能耗指标", "人员效率"],
        default=["设备利用率", "生产效率", "质量指标"]
    )

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("🔄 立即刷新", use_container_width=True):
        st.rerun()

    if st.button("📊 生成报告", use_container_width=True):
        generate_monitoring_report()

    if require_permission("monitoring.manage"):
        if st.button("🚨 创建告警", use_container_width=True):
            st.session_state.show_create_alert = True
            st.rerun()

    if st.button("📋 导出数据", use_container_width=True):
        export_monitoring_data()

# 主要内容区域
tab1, tab2, tab3 = st.tabs(["📊 实时监控", "🚨 异常管理", "🔄 计划调整"])

with tab1:
    st.markdown("#### 📊 实时监控面板")

    # 获取监控概览数据
    overview_data = get_monitoring_overview()

    if overview_data and overview_data.get("success"):
        overview = overview_data.get("data", {})

        # 关键指标展示
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            current_efficiency = overview.get("current_efficiency", 0)
            target_efficiency = overview.get("target_efficiency", 85)
            delta = current_efficiency - target_efficiency
            st.metric(
                "生产效率",
                f"{current_efficiency:.1f}%",
                delta=f"{delta:+.1f}%",
                delta_color="normal" if delta >= 0 else "inverse"
            )

        with col2:
            equipment_utilization = overview.get("equipment_utilization", 0)
            st.metric(
                "设备利用率",
                f"{equipment_utilization:.1f}%",
                delta=f"{equipment_utilization - 80:+.1f}%"
            )

        with col3:
            quality_rate = overview.get("quality_rate", 0)
            st.metric(
                "质量合格率",
                f"{quality_rate:.1f}%",
                delta=f"{quality_rate - 98:+.1f}%"
            )

        with col4:
            active_alerts = overview.get("active_alerts", 0)
            st.metric(
                "活跃告警",
                active_alerts,
                delta=f"{active_alerts - overview.get('yesterday_alerts', 0):+d}",
                delta_color="inverse" if active_alerts > 0 else "normal"
            )

        with col5:
            plan_completion = overview.get("plan_completion", 0)
            st.metric(
                "计划完成率",
                f"{plan_completion:.1f}%",
                delta=f"{plan_completion - 90:+.1f}%"
            )

        st.markdown("---")

        # 实时图表
        col1, col2 = st.columns(2)

        with col1:
            # 实时指标趋势
            metrics_data = get_real_time_metrics(selected_metrics)
            if metrics_data and metrics_data.get("success"):
                trend_data = metrics_data.get("data", {}).get("trends", [])

                if trend_data:
                    df_trends = pd.DataFrame(trend_data)

                    fig_trends = px.line(
                        df_trends,
                        x="timestamp",
                        y="value",
                        color="metric",
                        title="📈 实时指标趋势",
                        markers=True
                    )
                    fig_trends.update_layout(
                        xaxis_title="时间",
                        yaxis_title="数值",
                        legend_title="指标"
                    )
                    fig_trends = apply_plotly_theme(fig_trends)
                    st.plotly_chart(fig_trends, use_container_width=True)
                else:
                    st.info("暂无趋势数据")
            else:
                st.error("获取实时指标失败")

        with col2:
            # 资源利用率
            resource_data = get_resource_utilization()
            if resource_data and resource_data.get("success"):
                resources = resource_data.get("data", {}).get("resources", [])

                if resources:
                    df_resources = pd.DataFrame(resources)

                    fig_resources = px.bar(
                        df_resources,
                        x="resource_name",
                        y="utilization",
                        color="utilization",
                        title="📊 资源利用率",
                        color_continuous_scale="RdYlGn"
                    )
                    fig_resources.update_layout(
                        xaxis_title="资源",
                        yaxis_title="利用率 (%)",
                        showlegend=False
                    )
                    fig_resources = apply_plotly_theme(fig_resources)
                    st.plotly_chart(fig_resources, use_container_width=True)
                else:
                    st.info("暂无资源数据")
            else:
                st.error("获取资源利用率失败")

        # 设备状态概览
        st.markdown("##### ⚙️ 设备状态概览")

        equipment_status = overview.get("equipment_status", [])
        if equipment_status:
            # 创建设备状态表格
            for equipment in equipment_status:
                with st.container():
                    col1, col2, col3, col4, col5 = st.columns([2, 1, 1, 1, 1])

                    with col1:
                        status_color = get_status_color(equipment.get("status", "unknown"))
                        st.markdown(f"**{equipment.get('name', 'Unknown')}**")
                        st.markdown(f"<span style='color: {status_color}'>● {equipment.get('status_text', 'Unknown')}</span>",
                                  unsafe_allow_html=True)

                    with col2:
                        utilization = equipment.get("utilization", 0)
                        st.metric("利用率", f"{utilization:.1f}%", delta=None)

                    with col3:
                        efficiency = equipment.get("efficiency", 0)
                        st.metric("效率", f"{efficiency:.1f}%", delta=None)

                    with col4:
                        temperature = equipment.get("temperature", 0)
                        temp_color = "🔴" if temperature > 60 else "🟡" if temperature > 45 else "🟢"
                        st.metric("温度", f"{temperature:.1f}°C", delta=None)
                        st.markdown(f"{temp_color}")

                    with col5:
                        alerts = equipment.get("alerts", [])
                        if alerts:
                            st.warning(f"⚠️ {len(alerts)}个告警")
                        else:
                            st.success("✅ 正常")

                    st.markdown("---")
        else:
            st.info("暂无设备状态数据")

    else:
        st.error("获取监控概览失败")

with tab2:
    st.markdown("#### 🚨 异常管理")

    # 告警筛选
    col1, col2, col3 = st.columns(3)

    with col1:
        alert_level_filter = st.selectbox("告警级别", ["全部"] + alert_levels)

    with col2:
        alert_status_filter = st.selectbox("告警状态", ["全部", "未处理", "处理中", "已解决", "已忽略"])

    with col3:
        alert_time_filter = st.selectbox("时间范围", ["全部", "最近1小时", "最近24小时", "最近7天"])

    # 获取告警数据
    alerts_data = get_production_alerts(
        level=alert_level_filter if alert_level_filter != "全部" else None,
        status=alert_status_filter if alert_status_filter != "全部" else None
    )

    # 根据时间范围筛选（在前端进行筛选）
    if alert_time_filter != "全部" and alerts_data and alerts_data.get("success"):
        alerts = alerts_data.get("data", {}).get("alerts", [])
        filtered_alerts = filter_alerts_by_time(alerts, alert_time_filter)
        alerts_data["data"]["alerts"] = filtered_alerts
        alerts_data["data"]["total"] = len(filtered_alerts)

    if alerts_data and alerts_data.get("success"):
        alerts = alerts_data.get("data", {}).get("alerts", [])
        total_alerts = alerts_data.get("data", {}).get("total", 0)

        # 告警统计
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            critical_count = len([a for a in alerts if a.get("level") == "严重"])
            st.metric("严重告警", critical_count, delta_color="inverse")

        with col2:
            warning_count = len([a for a in alerts if a.get("level") == "警告"])
            st.metric("警告告警", warning_count)

        with col3:
            unhandled_count = len([a for a in alerts if a.get("status") == "未处理"])
            st.metric("未处理", unhandled_count, delta_color="inverse" if unhandled_count > 0 else "normal")

        with col4:
            st.metric("告警总数", total_alerts)

        st.markdown("---")

        # 告警列表
        if alerts:
            st.markdown("##### 📋 告警列表")

            for alert in alerts:
                with st.expander(f"{get_alert_icon(alert.get('level'))} {alert.get('title', 'Unknown Alert')}",
                               expanded=alert.get("level") == "严重"):

                    col1, col2 = st.columns([2, 1])

                    with col1:
                        st.write(f"**描述**: {alert.get('description', 'No description')}")
                        st.write(f"**来源**: {alert.get('source', 'Unknown')}")
                        st.write(f"**时间**: {alert.get('timestamp', 'Unknown')}")

                        if alert.get("details"):
                            st.write(f"**详情**: {alert['details']}")

                    with col2:
                        level_color = get_alert_level_color(alert.get("level"))
                        st.markdown(f"**级别**: <span style='color: {level_color}'>{alert.get('level', 'Unknown')}</span>",
                                  unsafe_allow_html=True)

                        status_color = get_alert_status_color(alert.get("status"))
                        st.markdown(f"**状态**: <span style='color: {status_color}'>{alert.get('status', 'Unknown')}</span>",
                                  unsafe_allow_html=True)

                        # 操作按钮
                        if require_permission("monitoring.manage") and alert.get("status") == "未处理":
                            col_a, col_b = st.columns(2)

                            with col_a:
                                if st.button("✅ 处理", key=f"handle_{alert.get('id')}"):
                                    handle_alert(alert.get('id'), "处理中")

                            with col_b:
                                if st.button("❌ 忽略", key=f"ignore_{alert.get('id')}"):
                                    handle_alert(alert.get('id'), "已忽略")
        else:
            st.info("暂无告警信息")

    else:
        st.error("获取告警数据失败")

with tab3:
    st.markdown("#### 🔄 计划调整")

    # 获取生产计划状态
    plans_data = get_production_plans_status()

    if plans_data and plans_data.get("success"):
        plans = plans_data.get("data", {}).get("plans", [])

        if plans:
            st.markdown("##### 📋 当前生产计划")

            # 计划概览
            col1, col2, col3 = st.columns(3)

            with col1:
                total_plans = len(plans)
                st.metric("计划总数", total_plans)

            with col2:
                on_schedule = len([p for p in plans if p.get("status") == "正常"])
                st.metric("正常执行", on_schedule, delta=f"{on_schedule/total_plans*100:.1f}%")

            with col3:
                delayed = len([p for p in plans if p.get("status") == "延期"])
                st.metric("延期计划", delayed, delta_color="inverse" if delayed > 0 else "normal")

            st.markdown("---")

            # 计划列表和调整
            for plan in plans:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 2])

                    with col1:
                        st.write(f"**{plan.get('name', 'Unknown Plan')}**")
                        st.write(f"产品: {plan.get('product', 'Unknown')}")
                        st.write(f"数量: {plan.get('quantity', 0)} {plan.get('unit', '件')}")

                    with col2:
                        progress = plan.get("progress", 0)
                        st.metric("进度", f"{progress:.1f}%")
                        st.progress(progress / 100)

                    with col3:
                        status = plan.get("status", "unknown")
                        status_color = get_plan_status_color(status)
                        st.markdown(f"**状态**")
                        st.markdown(f"<span style='color: {status_color}'>● {status}</span>",
                                  unsafe_allow_html=True)

                        priority = plan.get("priority", "normal")
                        priority_icon = get_priority_icon(priority)
                        st.markdown(f"{priority_icon} {priority}")

                    with col4:
                        if require_permission("monitoring.manage"):
                            if st.button("🔧 调整计划", key=f"adjust_{plan.get('id')}"):
                                st.session_state.adjust_plan_id = plan.get('id')
                                st.session_state.show_adjust_modal = True
                                st.rerun()

                            if plan.get("status") == "延期":
                                if st.button("⚡ 加急处理", key=f"urgent_{plan.get('id')}"):
                                    urgent_plan_adjustment(plan.get('id'))

                    st.markdown("---")
        else:
            st.info("暂无生产计划数据")

    else:
        st.error("获取生产计划状态失败")


# 辅助函数
def get_monitoring_overview():
    """获取监控概览"""
    try:
        result = api_client.get_production_monitoring_overview()

        # 如果API调用失败，返回模拟数据
        if not result.get("success"):
            return get_mock_monitoring_overview()

        return result
    except Exception as e:
        return get_mock_monitoring_overview()


def get_mock_monitoring_overview():
    """获取模拟监控概览数据"""
    return {
        "success": True,
        "data": {
            "current_efficiency": 87.5,
            "target_efficiency": 85.0,
            "equipment_utilization": 82.3,
            "quality_rate": 98.7,
            "active_alerts": 3,
            "yesterday_alerts": 5,
            "plan_completion": 92.1,
            "equipment_status": [
                {
                    "name": "数控机床-01",
                    "status": "running",
                    "status_text": "运行中",
                    "utilization": 85.2,
                    "efficiency": 88.1,
                    "temperature": 42.5,
                    "alerts": []
                },
                {
                    "name": "装配线-A",
                    "status": "running",
                    "status_text": "运行中",
                    "utilization": 92.8,
                    "efficiency": 90.3,
                    "temperature": 38.2,
                    "alerts": ["温度偏高"]
                },
                {
                    "name": "检测设备-03",
                    "status": "maintenance",
                    "status_text": "维护中",
                    "utilization": 0.0,
                    "efficiency": 0.0,
                    "temperature": 25.0,
                    "alerts": []
                }
            ]
        }
    }


def get_real_time_metrics(metrics):
    """获取实时指标数据"""
    try:
        result = api_client.get_real_time_metrics(metrics)

        if not result.get("success"):
            return get_mock_real_time_metrics(metrics)

        return result
    except Exception as e:
        return get_mock_real_time_metrics(metrics)


def get_mock_real_time_metrics(metrics):
    """获取模拟实时指标数据"""
    import random
    from datetime import datetime, timedelta

    trends = []
    base_time = datetime.now() - timedelta(hours=2)

    for i in range(24):  # 最近2小时，每5分钟一个点
        timestamp = base_time + timedelta(minutes=i * 5)

        for metric in metrics:
            if metric == "设备利用率":
                value = 75 + random.uniform(-10, 15)
            elif metric == "生产效率":
                value = 85 + random.uniform(-8, 12)
            elif metric == "质量指标":
                value = 96 + random.uniform(-2, 4)
            elif metric == "能耗指标":
                value = 80 + random.uniform(-15, 10)
            else:
                value = 70 + random.uniform(-20, 20)

            trends.append({
                "timestamp": timestamp.strftime("%H:%M"),
                "metric": metric,
                "value": max(0, min(100, value))
            })

    return {
        "success": True,
        "data": {
            "trends": trends
        }
    }


def get_resource_utilization():
    """获取资源利用率"""
    try:
        result = api_client.get_resource_utilization()

        if not result.get("success"):
            return get_mock_resource_utilization()

        return result
    except Exception as e:
        return get_mock_resource_utilization()


def get_mock_resource_utilization():
    """获取模拟资源利用率数据"""
    return {
        "success": True,
        "data": {
            "resources": [
                {"resource_name": "数控机床", "utilization": 85.2},
                {"resource_name": "装配线", "utilization": 92.8},
                {"resource_name": "检测设备", "utilization": 76.5},
                {"resource_name": "包装线", "utilization": 88.1},
                {"resource_name": "物流设备", "utilization": 72.3}
            ]
        }
    }


def get_production_alerts(level=None, status=None):
    """获取生产告警"""
    try:
        result = api_client.get_production_alerts(level=level, status=status)

        if not result.get("success"):
            return get_mock_production_alerts()

        return result
    except Exception as e:
        return get_mock_production_alerts()


def get_mock_production_alerts():
    """获取模拟生产告警数据"""
    return {
        "success": True,
        "data": {
            "alerts": [
                {
                    "id": "alert_001",
                    "title": "设备温度异常",
                    "description": "装配线-A温度超过安全阈值",
                    "level": "警告",
                    "status": "未处理",
                    "source": "装配线-A",
                    "timestamp": "2024-01-15 14:30:25",
                    "details": "当前温度: 65°C, 安全阈值: 60°C"
                },
                {
                    "id": "alert_002",
                    "title": "生产效率下降",
                    "description": "数控机床-01效率低于预期",
                    "level": "信息",
                    "status": "处理中",
                    "source": "数控机床-01",
                    "timestamp": "2024-01-15 13:45:12",
                    "details": "当前效率: 75%, 预期效率: 85%"
                },
                {
                    "id": "alert_003",
                    "title": "原材料库存不足",
                    "description": "钢材A型库存低于安全库存",
                    "level": "严重",
                    "status": "未处理",
                    "source": "库存管理系统",
                    "timestamp": "2024-01-15 12:20:08",
                    "details": "当前库存: 50kg, 安全库存: 100kg"
                }
            ],
            "total": 3
        }
    }


def get_production_plans_status():
    """获取生产计划状态"""
    try:
        result = api_client.get_production_plans_status()

        if not result.get("success"):
            return get_mock_production_plans()

        return result
    except Exception as e:
        return get_mock_production_plans()


def get_mock_production_plans():
    """获取模拟生产计划数据"""
    return {
        "success": True,
        "data": {
            "plans": [
                {
                    "id": "plan_001",
                    "name": "订单-2024-001",
                    "product": "精密零件A",
                    "quantity": 1000,
                    "unit": "件",
                    "progress": 75.5,
                    "status": "正常",
                    "priority": "高"
                },
                {
                    "id": "plan_002",
                    "name": "订单-2024-002",
                    "product": "装配件B",
                    "quantity": 500,
                    "unit": "套",
                    "progress": 45.2,
                    "status": "延期",
                    "priority": "紧急"
                },
                {
                    "id": "plan_003",
                    "name": "订单-2024-003",
                    "product": "标准件C",
                    "quantity": 2000,
                    "unit": "件",
                    "progress": 92.8,
                    "status": "正常",
                    "priority": "正常"
                }
            ]
        }
    }


# 状态和颜色辅助函数
def get_status_color(status):
    """获取状态颜色"""
    colors = {
        "running": "#2E8B57",
        "maintenance": "#FFD700",
        "fault": "#FF6347",
        "offline": "#808080"
    }
    return colors.get(status, "#333333")


def get_alert_icon(level):
    """获取告警图标"""
    icons = {
        "严重": "🔴",
        "警告": "🟡",
        "信息": "🔵"
    }
    return icons.get(level, "⚪")


def get_alert_level_color(level):
    """获取告警级别颜色"""
    colors = {
        "严重": "#FF4444",
        "警告": "#FFA500",
        "信息": "#4169E1"
    }
    return colors.get(level, "#666666")


def get_alert_status_color(status):
    """获取告警状态颜色"""
    colors = {
        "未处理": "#FF4444",
        "处理中": "#FFA500",
        "已解决": "#32CD32",
        "已忽略": "#808080"
    }
    return colors.get(status, "#666666")


def get_plan_status_color(status):
    """获取计划状态颜色"""
    colors = {
        "正常": "#32CD32",
        "延期": "#FF4444",
        "暂停": "#FFA500",
        "完成": "#4169E1"
    }
    return colors.get(status, "#666666")


def get_priority_icon(priority):
    """获取优先级图标"""
    icons = {
        "紧急": "🔴",
        "高": "🟡",
        "正常": "🟢",
        "低": "🔵"
    }
    return icons.get(priority, "⚪")


# 操作函数
def handle_alert(alert_id, status):
    """处理告警"""
    try:
        result = api_client.update_alert_status(alert_id, status)
        if result.get("success"):
            st.success(f"告警状态已更新为: {status}")
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"更新告警状态失败: {result.get('message', '未知错误')}")
    except Exception as e:
        st.error(f"处理告警失败: {str(e)}")


def urgent_plan_adjustment(plan_id):
    """紧急计划调整"""
    try:
        adjustments = {
            "priority": "紧急",
            "resource_allocation": "增加",
            "schedule_adjustment": "提前"
        }

        result = api_client.adjust_production_plan(plan_id, adjustments)
        if result.get("success"):
            st.success("计划已调整为加急处理")
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"计划调整失败: {result.get('message', '未知错误')}")
    except Exception as e:
        st.error(f"紧急调整失败: {str(e)}")


def generate_monitoring_report():
    """生成监控报告"""
    st.success("监控报告生成功能开发中...")


def export_monitoring_data():
    """导出监控数据"""
    st.success("数据导出功能开发中...")


def filter_alerts_by_time(alerts, time_filter):
    """根据时间范围筛选告警"""
    if time_filter == "全部":
        return alerts

    now = datetime.now()

    if time_filter == "最近1小时":
        cutoff_time = now - timedelta(hours=1)
    elif time_filter == "最近24小时":
        cutoff_time = now - timedelta(hours=24)
    elif time_filter == "最近7天":
        cutoff_time = now - timedelta(days=7)
    else:
        return alerts

    filtered_alerts = []
    for alert in alerts:
        try:
            # 解析告警时间戳
            alert_time = datetime.strptime(alert.get("timestamp", ""), "%Y-%m-%d %H:%M:%S")
            if alert_time >= cutoff_time:
                filtered_alerts.append(alert)
        except ValueError:
            # 如果时间戳格式不正确，保留告警
            filtered_alerts.append(alert)

    return filtered_alerts


# 自动刷新逻辑
if st.session_state.auto_refresh:
    # 初始化最后刷新时间
    if 'last_refresh_time' not in st.session_state:
        st.session_state.last_refresh_time = time.time()

    # 检查是否需要刷新
    current_time = time.time()
    time_since_refresh = current_time - st.session_state.last_refresh_time

    if time_since_refresh >= st.session_state.refresh_interval:
        st.session_state.last_refresh_time = current_time
        st.rerun()

    # 显示刷新状态
    remaining_time = st.session_state.refresh_interval - time_since_refresh
    if remaining_time > 0:
        st.sidebar.info(f"🔄 自动刷新: {remaining_time:.0f}秒后刷新")
