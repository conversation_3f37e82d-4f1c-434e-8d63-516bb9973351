"""
多语言功能测试页面
测试中英文界面切换和LLM对话功能
"""

import streamlit as st
from datetime import datetime

from utils.auth import check_authentication
from utils.i18n import i18n
from services.multilingual_llm_service import multilingual_llm_service

# 页面配置
st.set_page_config(
    page_title="多语言测试 - Smart APS",
    page_icon="🌐",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录" if i18n.get_current_language() == "zh" else "Please login first")
    st.stop()

# 页面标题
if i18n.get_current_language() == "en":
    st.title("🌐 Multilingual Function Test")
    st.markdown("### Test Chinese-English interface switching and LLM dialogue functions")
else:
    st.title("🌐 多语言功能测试")
    st.markdown("### 测试中英文界面切换和LLM对话功能")

# 侧边栏
with st.sidebar:
    # 语言选择器
    st.markdown("### 🌐 Language Settings")
    current_lang = i18n.language_selector("test_language_selector")
    
    st.markdown("---")
    
    # 当前语言信息
    st.markdown("### 📊 Current Language Info")
    st.write(f"**Current Language**: {current_lang}")
    st.write(f"**Interface Language**: {i18n.get_current_language()}")
    
    # 测试按钮
    st.markdown("### 🧪 Test Functions")
    
    if st.button(i18n.t("test_translation", default="测试翻译"), use_container_width=True):
        st.session_state.show_translation_test = True
    
    if st.button(i18n.t("test_llm_dialogue", default="测试LLM对话"), use_container_width=True):
        st.session_state.show_llm_test = True

# 主要内容区域
tab1, tab2, tab3 = st.tabs([
    i18n.t("interface_test", default="界面测试"),
    i18n.t("llm_test", default="LLM测试"),
    i18n.t("function_demo", default="功能演示")
])

with tab1:
    st.markdown(f"#### {i18n.t('interface_translation_test', default='界面翻译测试')}")
    
    # 测试各种UI组件的翻译
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 基础组件测试")
        
        # 按钮测试
        if st.button(i18n.t("save", default="保存")):
            st.success(i18n.t("operation_successful", default="操作成功"))
        
        # 选择框测试
        option = st.selectbox(
            i18n.t("select_option", default="选择选项"),
            [i18n.t("option1", default="选项1"), i18n.t("option2", default="选项2"), i18n.t("option3", default="选项3")]
        )
        
        # 滑块测试
        value = st.slider(
            i18n.t("adjust_value", default="调整数值"),
            0, 100, 50
        )
        
        # 输入框测试
        text_input = st.text_input(
            i18n.t("enter_text", default="输入文本"),
            placeholder=i18n.t("placeholder_text", default="请输入内容...")
        )
    
    with col2:
        st.markdown("##### 状态显示测试")
        
        # 指标测试
        st.metric(
            i18n.t("utilization_rate", default="利用率"),
            "85.2%",
            delta="2.1%"
        )
        
        # 状态消息测试
        st.success(f"✅ {i18n.t('success', default='成功')}")
        st.warning(f"⚠️ {i18n.t('warning', default='警告')}")
        st.error(f"❌ {i18n.t('error', default='错误')}")
        st.info(f"ℹ️ {i18n.t('info', default='信息')}")
        
        # 进度条测试
        progress_text = i18n.t("loading", default="加载中...")
        progress_bar = st.progress(0, text=progress_text)
        
        if st.button(i18n.t("start_progress", default="开始进度")):
            import time
            for i in range(101):
                progress_bar.progress(i, text=f"{progress_text} {i}%")
                time.sleep(0.01)

with tab2:
    st.markdown(f"#### {i18n.t('llm_dialogue_test', default='LLM对话测试')}")
    
    # 语言检测测试
    st.markdown("##### 语言检测测试")
    
    test_texts = [
        "你好，请帮我分析生产数据",
        "Hello, please help me analyze production data",
        "请优化生产计划",
        "Please optimize the production plan",
        "设备状态如何？",
        "How is the equipment status?"
    ]
    
    for text in test_texts:
        detected_lang = multilingual_llm_service.detect_language(text)
        st.write(f"**文本**: {text}")
        st.write(f"**检测语言**: {detected_lang}")
        st.write("---")
    
    # LLM对话测试
    st.markdown("##### LLM对话测试")
    
    # 测试消息输入
    test_message = st.text_area(
        i18n.t("test_message", default="测试消息"),
        placeholder=i18n.t("enter_test_message", default="输入测试消息...")
    )
    
    # 语言选择
    test_language = st.radio(
        i18n.t("test_language", default="测试语言"),
        ["auto", "zh", "en"],
        format_func=lambda x: {
            "auto": i18n.t("auto_detect", default="自动检测"),
            "zh": i18n.t("chinese", default="中文"),
            "en": i18n.t("english", default="English")
        }[x],
        horizontal=True
    )
    
    if st.button(i18n.t("test_llm_response", default="测试LLM回复")):
        if test_message:
            with st.spinner(i18n.t("generating_response", default="正在生成回复...")):
                try:
                    # 检测语言
                    if test_language == "auto":
                        detected_lang = multilingual_llm_service.detect_language(test_message)
                    else:
                        detected_lang = test_language
                    
                    # 生成回复
                    response = multilingual_llm_service._generate_intelligent_response(
                        f"用户当前问题: {test_message}" if detected_lang == "zh" else f"User's Current Question: {test_message}",
                        detected_lang
                    )
                    
                    # 显示结果
                    st.success(f"✅ {i18n.t('response_generated', default='回复生成成功')}")
                    st.write(f"**{i18n.t('detected_language', default='检测语言')}**: {detected_lang}")
                    st.write(f"**{i18n.t('ai_response', default='AI回复')}**:")
                    st.markdown(response)
                    
                except Exception as e:
                    st.error(f"❌ {i18n.t('test_failed', default='测试失败')}: {str(e)}")
        else:
            st.warning(i18n.t("please_enter_message", default="请输入测试消息"))

with tab3:
    st.markdown(f"#### {i18n.t('function_demo', default='功能演示')}")
    
    # 多语言模板演示
    st.markdown("##### 多语言模板演示")
    
    demo_scenarios = {
        "production_planning": {
            "zh": "请帮我优化生产计划",
            "en": "Please help me optimize the production plan"
        },
        "equipment": {
            "zh": "设备L01出现故障，请帮我分析",
            "en": "Equipment L01 has a malfunction, please help me analyze"
        },
        "quality": {
            "zh": "产品质量出现问题，需要改进建议",
            "en": "Product quality issues occurred, need improvement suggestions"
        },
        "inventory": {
            "zh": "库存管理需要优化，请提供方案",
            "en": "Inventory management needs optimization, please provide solutions"
        }
    }
    
    scenario = st.selectbox(
        i18n.t("select_scenario", default="选择场景"),
        list(demo_scenarios.keys()),
        format_func=lambda x: {
            "production_planning": i18n.t("production_planning", default="生产规划"),
            "equipment": i18n.t("equipment_management", default="设备管理"),
            "quality": i18n.t("quality_config", default="质量管理"),
            "inventory": i18n.t("inventory_management", default="库存管理")
        }[x]
    )
    
    if st.button(i18n.t("demo_scenario", default="演示场景")):
        current_lang = i18n.get_current_language()
        demo_message = demo_scenarios[scenario][current_lang]
        
        st.write(f"**{i18n.t('demo_message', default='演示消息')}**: {demo_message}")
        
        with st.spinner(i18n.t("generating_demo_response", default="正在生成演示回复...")):
            try:
                response = multilingual_llm_service._generate_intelligent_response(
                    f"用户当前问题: {demo_message}" if current_lang == "zh" else f"User's Current Question: {demo_message}",
                    current_lang
                )
                
                st.markdown("**AI回复**:" if current_lang == "zh" else "**AI Response**:")
                st.markdown(response)
                
            except Exception as e:
                st.error(f"❌ {i18n.t('demo_failed', default='演示失败')}: {str(e)}")
    
    # 功能统计
    st.markdown("##### 功能统计")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            i18n.t("supported_languages", default="支持语言"),
            "2",
            help="中文、英文"
        )
    
    with col2:
        st.metric(
            i18n.t("response_templates", default="回复模板"),
            "10",
            help="5种场景 × 2种语言"
        )
    
    with col3:
        st.metric(
            i18n.t("ui_translations", default="界面翻译"),
            "50+",
            help="覆盖主要UI组件"
        )
    
    with col4:
        st.metric(
            i18n.t("language_detection", default="语言检测"),
            "✅",
            help="自动检测中英文"
        )

# 页面底部信息
st.markdown("---")
if i18n.get_current_language() == "en":
    st.markdown("💡 **Tip**: The multilingual function supports automatic language detection and intelligent switching between Chinese and English interfaces.")
else:
    st.markdown("💡 **提示**: 多语言功能支持自动语言检测和中英文界面智能切换。")
